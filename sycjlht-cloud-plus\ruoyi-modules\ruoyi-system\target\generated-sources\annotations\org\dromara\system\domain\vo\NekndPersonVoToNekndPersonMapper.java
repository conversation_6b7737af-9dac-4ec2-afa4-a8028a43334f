package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__984;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.NekndPerson;
import org.dromara.system.domain.NekndPersonToNekndPersonVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__984.class,
    uses = {NekndPersonToNekndPersonVoMapper.class},
    imports = {}
)
public interface NekndPersonVoToNekndPersonMapper extends BaseMapper<NekndPersonVo, NekndPerson> {
}
