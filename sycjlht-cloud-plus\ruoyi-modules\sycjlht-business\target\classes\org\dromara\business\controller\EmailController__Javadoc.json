{"doc": "\n 邮件推荐控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendEmail", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发送简单邮件\r\n"}, {"name": "employEmailRecommended", "paramTypes": ["java.lang.Integer"], "doc": "\n 邮件推荐岗位\r\n 查询所有的用户，获取jobType，然后根据jobType查询所有正常的岗位，然后批量发送邮件\r\n 目前是填了期望岗位类型的用户，才会推荐岗位\r\n"}, {"name": "renderJobTemplate", "paramTypes": ["java.util.List"], "doc": "\n 渲染岗位模版\r\n"}, {"name": "personRecommended", "paramTypes": ["java.lang.Integer"], "doc": "\n 通过邮件推荐个人给公司\r\n\r\n @param userId 公司的userId ,传了公司的userId会只给这家公司推荐相应的人才\r\n"}, {"name": "renderPersonTemplate", "paramTypes": ["java.util.List"], "doc": "\n 渲染人才模版\r\n"}], "constructors": []}