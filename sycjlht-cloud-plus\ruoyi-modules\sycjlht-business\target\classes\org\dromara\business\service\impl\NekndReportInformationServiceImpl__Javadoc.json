{"doc": "\n 报告管理Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-09-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndReportInformationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询报告管理\r\n\r\n @param id 报告管理主键\r\n @return 报告管理\r\n"}, {"name": "selectNekndReportInformationList", "paramTypes": ["org.dromara.business.domain.NekndReportInformation"], "doc": "\n 查询报告管理列表\r\n\r\n @param nekndReportInformation 报告管理\r\n @return 报告管理\r\n"}, {"name": "insertNekndReportInformation", "paramTypes": ["org.dromara.business.domain.NekndReportInformation"], "doc": "\n 新增报告管理\r\n\r\n @param nekndReportInformation 报告管理\r\n @return 结果\r\n"}, {"name": "updateNekndReportInformation", "paramTypes": ["org.dromara.business.domain.NekndReportInformation"], "doc": "\n 修改报告管理\r\n\r\n @param nekndReportInformation 报告管理\r\n @return 结果\r\n"}, {"name": "deleteNekndReportInformationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除报告管理\r\n\r\n @param ids 需要删除的报告管理主键\r\n @return 结果\r\n"}, {"name": "deleteNekndReportInformationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除报告管理信息\r\n\r\n @param id 报告管理主键\r\n @return 结果\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndReportInformation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询报告管理列表\r\n\r\n @param nekndReportInformation 报告管理\r\n @param pageQuery 分页查询\r\n @return 报告管理集合\r\n"}, {"name": "queryPublicPageList", "paramTypes": ["org.dromara.business.domain.NekndReportInformation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询公开报告管理列表\r\n\r\n @param nekndReportInformation 报告管理\r\n @param pageQuery 分页查询\r\n @return 报告管理集合\r\n"}], "constructors": []}