{"doc": "\n 留言主题对象 neknd_message_topic\r\n\r\n <AUTHOR>\r\n @date 2024-08-09\r\n", "fields": [{"name": "id", "doc": "主键id "}, {"name": "delFlag", "doc": "删除标志（0代表存在 2代表删除） "}, {"name": "status", "doc": "状态(1:岗位需求,2:企业需求,3其他) "}, {"name": "recipientUserId", "doc": "收件用户id "}, {"name": "recipientUserName", "doc": "收件用户名称 "}, {"name": "recipientDeptId", "doc": "收件用户部门id "}, {"name": "avatar", "doc": "发送人用户头像地址 "}, {"name": "recipient<PERSON><PERSON><PERSON>", "doc": "收件人用户头像地址 "}, {"name": "title", "doc": "留言标题 "}, {"name": "readStatus", "doc": "已读状态(0:未读,1:已读) "}, {"name": "senderUserId", "doc": "发件用户id "}, {"name": "senderUserName", "doc": "发件用户名称 "}, {"name": "senderDeptId", "doc": "发件用户部门id "}, {"name": "unReadCount", "doc": "前端字段 "}, {"name": "companyName", "doc": "收件人公司名称 "}, {"name": "currentUserId", "doc": "当前用户id "}, {"name": "displayName", "doc": "显示名称 "}, {"name": "firstMessageContent", "doc": "第一条消息内容 "}], "enumConstants": [], "methods": [{"name": "getTopicTitle", "paramTypes": [], "doc": "\n 获取主题标题（别名方法）\r\n @return 主题标题\r\n"}, {"name": "setTopicTitle", "paramTypes": ["java.lang.String"], "doc": "\n 设置主题标题（别名方法）\r\n @param topicTitle 主题标题\r\n"}], "constructors": []}