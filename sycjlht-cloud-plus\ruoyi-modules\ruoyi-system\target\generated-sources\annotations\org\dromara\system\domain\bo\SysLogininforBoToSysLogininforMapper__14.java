package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__960;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysLogininfor;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__960.class,
    uses = {},
    imports = {}
)
public interface SysLogininforBoToSysLogininforMapper__14 extends BaseMapper<SysLogininforBo, SysLogininfor> {
}
