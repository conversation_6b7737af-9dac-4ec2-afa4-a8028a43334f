{"doc": "\n 用户点赞记录Controller\r\n \r\n <AUTHOR>\r\n @date 2025-04-03\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndLikeRecords", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询用户点赞记录列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndLikeRecords"], "doc": "\n 导出用户点赞记录列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户点赞记录详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndLikeRecords"], "doc": "\n 新增用户点赞记录\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndLikeRecords"], "doc": "\n 修改用户点赞记录\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除用户点赞记录\r\n"}, {"name": "toggleLike", "paramTypes": ["java.lang.Integer"], "doc": "\n 点赞和取消点赞切换\r\n"}, {"name": "checkLike", "paramTypes": ["java.lang.Integer"], "doc": "\n 检查用户是否已点赞课程\r\n"}, {"name": "getMyLikes", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取用户点赞的课程列表\r\n"}, {"name": "getCourseStats", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取课程的点赞统计\r\n"}, {"name": "batchCancelLike", "paramTypes": ["java.util.List"], "doc": "\n 批量取消点赞\r\n"}], "constructors": []}