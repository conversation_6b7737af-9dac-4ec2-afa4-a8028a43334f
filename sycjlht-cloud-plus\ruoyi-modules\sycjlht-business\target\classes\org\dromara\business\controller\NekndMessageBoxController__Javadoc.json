{"doc": "\n 留言箱Controller\r\n \r\n <AUTHOR>\r\n @date 2024-06-02\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndMessageBox", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询留言箱列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndMessageBox"], "doc": "\n 导出留言箱列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取留言箱详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": "\n 新增留言\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": "\n 修改留言\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除留言\r\n"}, {"name": "sendToMultiple", "paramTypes": ["org.dromara.business.domain.NekndMessageBox", "java.util.List"], "doc": "\n 发送留言给多个用户\r\n"}, {"name": "mark<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long"], "doc": "\n 标记消息为已读\r\n"}, {"name": "getUnreadCount", "paramTypes": [], "doc": "\n 获取未读消息数量\r\n"}, {"name": "getMessageTopics", "paramTypes": [], "doc": "\n 获取消息主题列表\r\n"}, {"name": "reply", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": "\n 回复消息\r\n"}, {"name": "getConversation", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取对话记录\r\n"}], "constructors": []}