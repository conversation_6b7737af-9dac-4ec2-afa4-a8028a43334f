{"doc": " 任务分配人枚举\n\n <AUTHOR>\n", "fields": [], "enumConstants": [{"name": "USER", "doc": " 用户\n"}, {"name": "ROLE", "doc": " 角色\n"}, {"name": "DEPT", "doc": " 部门\n"}, {"name": "POST", "doc": " 岗位\n"}], "methods": [{"name": "fromDesc", "paramTypes": ["java.lang.String"], "doc": " 根据描述获取对应的枚举类型\n <p>\n 通过传入描述，查找并返回匹配的枚举项。如果未找到匹配项，会抛出 {@link ServiceException}。\n </p>\n\n @param desc 描述，用于匹配对应的枚举项\n @return TaskAssigneeEnum 返回对应的枚举类型\n @throws ServiceException 如果未找到匹配的枚举项\n"}, {"name": "fromCode", "paramTypes": ["java.lang.String"], "doc": " 根据代码获取对应的枚举类型\n <p>\n 通过传入代码，查找并返回匹配的枚举项。如果未找到匹配项，会抛出 {@link ServiceException}。\n </p>\n\n @param code 代码，用于匹配对应的枚举项\n @return TaskAssigneeEnum 返回对应的枚举类型\n @throws IllegalArgumentException 如果未找到匹配的枚举项\n"}, {"name": "getAssigneeTypeList", "paramTypes": [], "doc": " 获取所有办理人类型的描述列表\n <p>\n 获取当前枚举类所有项的描述字段列表，通常用于展示选择项。\n </p>\n\n @return List<String> 返回所有办理人类型的描述列表\n"}, {"name": "getAssigneeCodeList", "paramTypes": [], "doc": " 获取所有办理人类型的代码列表\n <p>\n 获取当前枚举类所有项的代码字段列表，通常用于程序内部逻辑的判断。\n </p>\n\n @return List<String> 返回所有办理人类型的代码列表\n"}], "constructors": []}