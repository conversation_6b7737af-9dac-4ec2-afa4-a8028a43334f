{"doc": "\n 企业信息Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-05-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPageNekndCompanyList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 分页查询企业信息列表\r\n\r\n @param page 分页对象\r\n @param queryWrapper 查询条件\r\n @return 企业信息分页数据\r\n"}], "constructors": []}