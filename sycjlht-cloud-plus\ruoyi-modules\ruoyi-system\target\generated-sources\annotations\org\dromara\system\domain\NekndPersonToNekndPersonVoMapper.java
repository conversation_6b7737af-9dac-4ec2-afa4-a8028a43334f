package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__984;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.NekndPersonBoToNekndPersonMapper;
import org.dromara.system.domain.vo.NekndPersonVo;
import org.dromara.system.domain.vo.NekndPersonVoToNekndPersonMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__984.class,
    uses = {NekndPersonBoToNekndPersonMapper.class,NekndPersonVoToNekndPersonMapper.class},
    imports = {}
)
public interface NekndPersonToNekndPersonVoMapper extends BaseMapper<NekndPerson, NekndPersonVo> {
}
