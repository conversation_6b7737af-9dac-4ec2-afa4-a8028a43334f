{"doc": "\n 服务需求Controller\r\n \r\n <AUTHOR>\r\n @date 2024-05-30\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询服务需求列表\r\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 门户查询服务需求列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndServiceRequirements"], "doc": "\n 导出服务需求列表\r\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": "\n 导入服务需求\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取服务需求详细信息\r\n"}, {"name": "getInfoPublic", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取服务需求详细信息（门户使用）\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements"], "doc": "\n 新增服务需求\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements"], "doc": "\n 修改服务需求\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除服务需求\r\n"}, {"name": "getRecommendedServices", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询推荐服务需求列表\r\n"}, {"name": "auditing", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": "\n 审核服务需求\r\n"}, {"name": "getMyServiceRequirements", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取当前用户的服务需求\r\n"}, {"name": "filterByField", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 按服务领域筛选\r\n"}, {"name": "filterByRegion", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 按地区筛选服务需求\r\n"}, {"name": "getPopularServices", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取热门服务需求\r\n"}], "constructors": []}