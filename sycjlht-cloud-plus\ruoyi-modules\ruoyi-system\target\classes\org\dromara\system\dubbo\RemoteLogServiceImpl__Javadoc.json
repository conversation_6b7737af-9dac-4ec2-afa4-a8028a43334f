{"doc": "\n 操作日志记录\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "saveLog", "paramTypes": ["org.dromara.system.api.domain.bo.RemoteOperLogBo"], "doc": "\n 保存系统日志\r\n\r\n @param remoteOperLogBo 日志实体\r\n"}, {"name": "saveLogininfor", "paramTypes": ["org.dromara.system.api.domain.bo.RemoteLogininforBo"], "doc": "\n 保存访问记录\r\n\r\n @param remoteLogininforBo 访问实体\r\n"}], "constructors": []}