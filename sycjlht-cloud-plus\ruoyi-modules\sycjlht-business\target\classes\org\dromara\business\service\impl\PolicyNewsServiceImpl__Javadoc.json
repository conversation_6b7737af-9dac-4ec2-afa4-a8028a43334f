{"doc": "\n 政策新闻信息Service业务层处理\r\n 整合了现代化架构和单体业务逻辑\r\n\r\n <AUTHOR>\r\n @date 2025-01-15\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询政策新闻信息\r\n\r\n @param id 新闻ID\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询政策新闻信息列表\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": "\n 查询政策新闻信息列表\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": "\n 新增政策新闻信息\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": "\n 修改政策新闻信息\r\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.business.domain.PolicyNews"], "doc": "\n 保存前的数据校验\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": "\n 批量删除政策新闻信息\r\n"}, {"name": "auditNews", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 审核政策新闻\r\n"}, {"name": "getData", "paramTypes": [], "doc": "\n 获取政策新闻统计数据\r\n"}, {"name": "importUser", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 导入政策新闻数据\r\n"}], "constructors": []}