{"doc": "\n 行业管理Controller\r\n \r\n <AUTHOR>\r\n @date 2024-12-23\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndIndustry", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询行业管理列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndIndustry"], "doc": "\n 导出行业管理列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取行业管理详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndIndustry"], "doc": "\n 新增行业管理\r\n"}, {"name": "update", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long", "java.lang.String"], "doc": "\n 修改行业管理（文件上传更新）\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除行业管理\r\n"}, {"name": "getProfessionalGroupsAccount", "paramTypes": ["java.lang.Long"], "doc": "\n 查询专业群占比数据\r\n"}, {"name": "getTextMessage", "paramTypes": ["java.lang.Long"], "doc": "\n 查询文本信息\r\n"}, {"name": "getProportionTeachers", "paramTypes": ["java.lang.Long"], "doc": "\n 查询教师比例数据\r\n"}, {"name": "getValueChangeTrend", "paramTypes": ["java.lang.Long"], "doc": "\n 查询产值变化趋势\r\n"}, {"name": "getPolicyNumber", "paramTypes": ["java.lang.Long"], "doc": "\n 查询政策支持数量\r\n"}, {"name": "getSchoolCompanyCooperation", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询校企合作项目\r\n"}, {"name": "getJobRequirements", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询岗位要求\r\n"}, {"name": "getCooperationStrength", "paramTypes": ["java.lang.Long"], "doc": "\n 查询合作强度数据\r\n"}], "constructors": []}