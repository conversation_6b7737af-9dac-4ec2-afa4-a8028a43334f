{"doc": "\n 供需管理控制器\r\n\r\n <AUTHOR>\r\n @date 2024-05-13\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询项目需求列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndSupplyDemand"], "doc": "\n 导出项目需求列表\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取项目需求信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": "\n 新增项目需求\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": "\n 修改项目需求\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除项目需求\r\n"}, {"name": "dockingStatus", "paramTypes": ["java.lang.Integer"], "doc": "\n 门户网站查看对接状态\r\n"}, {"name": "dockingRecord", "paramTypes": ["java.lang.Integer", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 企业后台项目对接记录\r\n"}, {"name": "getInfoDetail", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取项目需求详细信息\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询项目推荐列表\r\n"}, {"name": "sortList", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询项目需求列表,并排序\r\n"}, {"name": "get<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 企业查询自己的项目需求列表\r\n"}, {"name": "audit", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": "\n 审核项目需求\r\n 1.审核通过 2.审核不通过\r\n"}, {"name": "getListAudit", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查看项目审核列表\r\n"}, {"name": "getDemandStatus", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据项目需求id查询项目需求的对接状态\r\n"}, {"name": "getData", "paramTypes": [], "doc": "\n 首页企业需求数据获取\r\n"}], "constructors": []}