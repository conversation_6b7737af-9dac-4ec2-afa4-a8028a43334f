{"doc": "\n 短信服务\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getSmsBlend", "paramTypes": [], "doc": "\n 获取特定供应商类型的 SmsBlend 实例\r\n\r\n @return SmsBlend 实例，代表指定供应商类型\r\n"}, {"name": "getRemoteSms", "paramTypes": ["org.dromara.sms4j.api.entity.SmsResponse"], "doc": "\n 根据给定的 SmsResponse 对象创建并返回一个 RemoteSms 对象，封装短信发送的响应信息\r\n\r\n @param smsResponse 短信发送的响应信息\r\n @return 封装了短信发送结果的 RemoteSms 对象\r\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 同步方法：发送简单文本短信\r\n\r\n @param phone   目标手机号\r\n @param message 短信内容\r\n @return 封装了短信发送结果的 RemoteSms 对象\r\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.String", "java.util.LinkedHashMap"], "doc": "\n 同步方法：发送固定消息模板多模板参数短信\r\n\r\n @param phone    目标手机号\r\n @param messages 短信模板参数，使用 LinkedHashMap 以保持参数顺序\r\n @return 封装了短信发送结果的 RemoteSms 对象\r\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.LinkedHashMap"], "doc": "\n 同步方法：发送带参数的短信\r\n\r\n @param phone      目标手机号\r\n @param templateId 短信模板ID\r\n @param messages   短信模板参数，使用 LinkedHashMap 以保持参数顺序\r\n @return 封装了短信发送结果的 RemoteSms 对象\r\n"}, {"name": "messageTexting", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 同步方法：群发简单文本短信\r\n\r\n @param phones  目标手机号列表\r\n @param message 短信内容\r\n @return 封装了短信发送结果的 RemoteSms 对象\r\n"}, {"name": "messageTexting", "paramTypes": ["java.util.List", "java.lang.String", "java.util.LinkedHashMap"], "doc": "\n 同步方法：群发带参数的短信\r\n\r\n @param phones     目标手机号列表\r\n @param templateId 短信模板ID\r\n @param messages   短信模板参数，使用 LinkedHashMap 以保持参数顺序\r\n @return 封装了短信发送结果的 RemoteSms 对象\r\n"}, {"name": "sendMessageAsync", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 异步方法：发送简单文本短信\r\n\r\n @param phone   目标手机号\r\n @param message 短信内容\r\n"}, {"name": "sendMessageAsync", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.LinkedHashMap"], "doc": "\n 异步方法：发送带参数的短信\r\n\r\n @param phone      目标手机号\r\n @param templateId 短信模板ID\r\n @param messages   短信模板参数，使用 LinkedHashMap 以保持参数顺序\r\n"}, {"name": "delayMessage", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long"], "doc": "\n 延迟发送简单文本短信\r\n\r\n @param phone       目标手机号\r\n @param message     短信内容\r\n @param delayedTime 延迟发送时间（毫秒）\r\n"}, {"name": "delayMessage", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.LinkedHashMap", "java.lang.Long"], "doc": "\n 延迟发送带参数的短信\r\n\r\n @param phone       目标手机号\r\n @param templateId  短信模板ID\r\n @param messages    短信模板参数，使用 LinkedHashMap 以保持参数顺序\r\n @param delayedTime 延迟发送时间（毫秒）\r\n"}, {"name": "delayMessageTexting", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Long"], "doc": "\n 延迟群发简单文本短信\r\n\r\n @param phones      目标手机号列表\r\n @param message     短信内容\r\n @param delayedTime 延迟发送时间（毫秒）\r\n"}, {"name": "delayMessageTexting", "paramTypes": ["java.util.List", "java.lang.String", "java.util.LinkedHashMap", "java.lang.Long"], "doc": "\n 延迟批量发送带参数的短信\r\n\r\n @param phones      目标手机号列表\r\n @param templateId  短信模板ID\r\n @param messages    短信模板参数，使用 LinkedHashMap 以保持参数顺序\r\n @param delayedTime 延迟发送时间（毫秒）\r\n"}, {"name": "addBlacklist", "paramTypes": ["java.lang.String"], "doc": "\n 加入黑名单\r\n\r\n @param phone 手机号\r\n"}, {"name": "addBlacklist", "paramTypes": ["java.util.List"], "doc": "\n 加入黑名单\r\n\r\n @param phones 手机号列表\r\n"}, {"name": "removeBlacklist", "paramTypes": ["java.lang.String"], "doc": "\n 移除黑名单\r\n\r\n @param phone 手机号\r\n"}, {"name": "removeBlacklist", "paramTypes": ["java.util.List"], "doc": "\n 移除黑名单\r\n\r\n @param phones 手机号\r\n"}], "constructors": []}