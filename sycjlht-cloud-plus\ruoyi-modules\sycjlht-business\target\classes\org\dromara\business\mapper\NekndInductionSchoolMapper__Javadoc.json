{"doc": "\n 引产入校信息Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-12-26\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndInductionSchoolById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询引产入校信息\r\n\r\n @param id 引产入校信息主键\r\n @return 引产入校信息\r\n"}, {"name": "selectNekndInductionSchoolList", "paramTypes": ["org.dromara.business.domain.NekndInductionSchool"], "doc": "\n 查询引产入校信息列表\r\n\r\n @param nekndInductionSchool 引产入校信息\r\n @return 引产入校信息集合\r\n"}, {"name": "insertNekndInductionSchool", "paramTypes": ["org.dromara.business.domain.NekndInductionSchool"], "doc": "\n 新增引产入校信息\r\n\r\n @param nekndInductionSchool 引产入校信息\r\n @return 结果\r\n"}, {"name": "updateNekndInductionSchool", "paramTypes": ["org.dromara.business.domain.NekndInductionSchool"], "doc": "\n 修改引产入校信息\r\n\r\n @param nekndInductionSchool 引产入校信息\r\n @return 结果\r\n"}, {"name": "deleteNekndInductionSchoolById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除引产入校信息\r\n\r\n @param id 引产入校信息主键\r\n @return 结果\r\n"}, {"name": "deleteNekndInductionSchoolByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除引产入校信息\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}, {"name": "selectCollegeStats", "paramTypes": ["java.lang.String"], "doc": "\n 批量删除引产入校信息\r\n\r\n @return 结果\r\n"}], "constructors": []}