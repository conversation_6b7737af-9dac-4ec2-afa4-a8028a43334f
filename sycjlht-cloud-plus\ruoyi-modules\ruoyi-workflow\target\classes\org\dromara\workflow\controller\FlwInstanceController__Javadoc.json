{"doc": "\n 流程实例管理 控制层\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectRunningInstanceList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInstanceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询正在运行的流程实例列表\r\n\r\n @param flowInstanceBo 流程实例\r\n @param pageQuery      分页\r\n"}, {"name": "selectFinishInstanceList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInstanceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询已结束的流程实例列表\r\n\r\n @param flowInstanceBo 流程实例\r\n @param pageQuery      分页\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 根据业务id查询流程实例详细信息\r\n\r\n @param businessId 业务id\r\n"}, {"name": "deleteByBusinessIds", "paramTypes": ["java.util.List"], "doc": "\n 按照业务id删除流程实例\r\n\r\n @param businessIds 业务id\r\n"}, {"name": "deleteByInstanceIds", "paramTypes": ["java.util.List"], "doc": "\n 按照实例id删除流程实例\r\n\r\n @param instanceIds 实例id\r\n"}, {"name": "cancelProcessApply", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCancelBo"], "doc": "\n 撤销流程\r\n\r\n @param bo 参数\r\n"}, {"name": "active", "paramTypes": ["java.lang.Long", "boolean"], "doc": "\n 激活/挂起流程实例\r\n\r\n @param id     流程实例id\r\n @param active 激活/挂起\r\n"}, {"name": "selectCurrentInstanceList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInstanceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取当前登陆人发起的流程实例\r\n\r\n @param flowInstanceBo 参数\r\n @param pageQuery      分页\r\n"}, {"name": "flowHisTaskList", "paramTypes": ["java.lang.String"], "doc": "\n 获取流程图，流程记录\r\n\r\n @param businessId 业务id\r\n"}, {"name": "instanceVariable", "paramTypes": ["java.lang.Long"], "doc": "\n 获取流程变量\r\n\r\n @param instanceId 流程实例id\r\n"}, {"name": "invalid", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInvalidBo"], "doc": "\n 作废流程\r\n\r\n @param bo 参数\r\n"}], "constructors": []}