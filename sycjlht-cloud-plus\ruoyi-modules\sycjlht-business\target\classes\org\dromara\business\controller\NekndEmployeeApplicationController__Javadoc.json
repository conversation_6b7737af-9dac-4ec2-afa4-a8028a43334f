{"doc": "\n 公司和学校的员工申请记录Controller\r\n\r\n <AUTHOR>\r\n @date 2024-09-05\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询公司和学校的员工申请记录列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndEmployeeApplication"], "doc": "\n 导出公司和学校的员工申请记录列表\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取公司和学校的员工申请记录详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": "\n 新增公司和学校的员工申请记录\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": "\n 修改公司和学校的员工申请记录\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除公司和学校的员工申请记录\r\n"}], "constructors": []}