{"doc": "\n 市级管理Controller\r\n \r\n <AUTHOR>\r\n @date 2024-05-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity"], "doc": "\n 获取城市列表（湖北省咸宁市优先显示）\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取市级详细信息\r\n"}, {"name": "list", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询城市列表（分页）\r\n"}, {"name": "getByProvince", "paramTypes": ["java.lang.Long"], "doc": "\n 按省份ID查询城市\r\n"}, {"name": "search", "paramTypes": ["java.lang.String"], "doc": "\n 搜索城市\r\n"}, {"name": "getStatistics", "paramTypes": [], "doc": "\n 获取城市统计信息\r\n"}, {"name": "getPopular", "paramTypes": [], "doc": "\n 获取热门城市\r\n"}], "constructors": []}