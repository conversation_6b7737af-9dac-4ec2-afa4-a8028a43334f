{"doc": "\n 流程定义 服务层实现\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryList", "paramTypes": ["org.dromara.warm.flow.orm.entity.FlowDefinition", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询流程定义列表\r\n\r\n @param flowDefinition 流程定义信息\r\n @param pageQuery      分页\r\n @return 返回分页列表\r\n"}, {"name": "unPublishList", "paramTypes": ["org.dromara.warm.flow.orm.entity.FlowDefinition", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询未发布的流程定义列表\r\n\r\n @param flowDefinition 流程定义信息\r\n @param pageQuery      分页\r\n @return 返回分页列表\r\n"}, {"name": "publish", "paramTypes": ["java.lang.Long"], "doc": "\n 发布流程定义\r\n\r\n @param id 流程定义id\r\n"}, {"name": "importJson", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": "\n 导入流程定义\r\n\r\n @param file 文件\r\n"}, {"name": "exportDef", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出流程定义\r\n\r\n @param id       流程定义id\r\n @param response 响应\r\n @throws IOException 异常\r\n"}, {"name": "removeDef", "paramTypes": ["java.util.List"], "doc": "\n 删除流程定义\r\n\r\n @param ids 流程定义id\r\n"}, {"name": "syncDef", "paramTypes": ["java.lang.String"], "doc": "\n 新增租户流程定义\r\n\r\n @param tenantId 租户id\r\n"}], "constructors": []}