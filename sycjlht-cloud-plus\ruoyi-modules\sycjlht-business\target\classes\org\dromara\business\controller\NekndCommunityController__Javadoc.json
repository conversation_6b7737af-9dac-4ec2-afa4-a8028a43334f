{"doc": "\n 共同体Controller\r\n \r\n <AUTHOR>\r\n @date 2025-01-02\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCommunity", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询共同体列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCommunity"], "doc": "\n 导出共同体列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取共同体详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCommunity"], "doc": "\n 新增共同体\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCommunity"], "doc": "\n 修改共同体\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除共同体\r\n"}, {"name": "joinCommunity", "paramTypes": ["java.lang.Long"], "doc": "\n 加入共同体\r\n"}, {"name": "leaveCommunity", "paramTypes": ["java.lang.Long"], "doc": "\n 离开共同体\r\n"}, {"name": "getMyCommunities", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取用户加入的共同体\r\n"}, {"name": "getCommunityMembers", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取共同体成员列表\r\n"}, {"name": "getPopularCommunities", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取热门共同体\r\n"}, {"name": "searchCommunities", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 搜索共同体\r\n"}, {"name": "getStatistics", "paramTypes": [], "doc": "\n 获取共同体统计信息\r\n"}, {"name": "getByType", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 按类型查询共同体\r\n"}, {"name": "checkMembership", "paramTypes": ["java.lang.Long"], "doc": "\n 检查用户是否已加入共同体\r\n"}, {"name": "updateCommunityActivity", "paramTypes": [], "doc": "\n 定时任务：更新共同体活跃度\r\n"}], "constructors": []}