{"doc": "\n 留言主题Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-08-09\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndMessageTopicById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询留言主题\r\n\r\n @param id 留言主题主键\r\n @return 留言主题\r\n"}, {"name": "selectNekndMessageTopicList", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": "\n 查询留言主题列表\r\n\r\n @param nekndMessageTopic 留言主题\r\n @return 留言主题集合\r\n"}, {"name": "insertNekndMessageTopic", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": "\n 新增留言主题\r\n\r\n @param nekndMessageTopic 留言主题\r\n @return 结果\r\n"}, {"name": "updateNekndMessageTopic", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": "\n 修改留言主题\r\n\r\n @param nekndMessageTopic 留言主题\r\n @return 结果\r\n"}, {"name": "deleteNekndMessageTopicByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除留言主题\r\n\r\n @param ids 需要删除的留言主题主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndMessageTopicById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除留言主题信息\r\n\r\n @param id 留言主题主键\r\n @return 结果\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询分页列表\r\n\r\n @param nekndMessageTopic 查询条件\r\n @param pageQuery 分页参数\r\n @return 留言主题集合\r\n"}, {"name": "createTopic", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": "\n 创建主题\r\n\r\n @param nekndMessageTopic 留言主题\r\n @return 结果\r\n"}, {"name": "logicalDeleteForUser", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": "\n 为用户逻辑删除\r\n\r\n @param topicId 主题ID\r\n @param userId 用户ID\r\n @return 结果\r\n"}, {"name": "markTopicAsRead", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": "\n 标记主题为已读\r\n\r\n @param topicId 主题ID\r\n @param userId 用户ID\r\n @return 结果\r\n"}, {"name": "getUnreadTopicCount", "paramTypes": ["java.lang.Long"], "doc": "\n 获取未读主题数量\r\n\r\n @param userId 用户ID\r\n @return 未读数量\r\n"}, {"name": "searchTopicsForUser", "paramTypes": ["java.lang.Long", "java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 为用户搜索主题\r\n\r\n @param userId 用户ID\r\n @param keyword 关键词\r\n @param pageQuery 分页参数\r\n @return 主题集合\r\n"}], "constructors": []}