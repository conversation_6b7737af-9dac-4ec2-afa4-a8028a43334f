{"doc": "\n 公司和学校的员工申请记录Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-09-05\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndEmployeeApplicationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询公司和学校的员工申请记录\r\n\r\n @param id 公司和学校的员工申请记录主键\r\n @return 公司和学校的员工申请记录\r\n"}, {"name": "selectNekndEmployeeApplicationList", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": "\n 查询公司和学校的员工申请记录列表\r\n\r\n @param nekndEmployeeApplication 公司和学校的员工申请记录\r\n @return 公司和学校的员工申请记录集合\r\n"}, {"name": "insertNekndEmployeeApplication", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": "\n 新增公司和学校的员工申请记录\r\n\r\n @param nekndEmployeeApplication 公司和学校的员工申请记录\r\n @return 结果\r\n"}, {"name": "updateNekndEmployeeApplication", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": "\n 修改公司和学校的员工申请记录\r\n\r\n @param nekndEmployeeApplication 公司和学校的员工申请记录\r\n @return 结果\r\n"}, {"name": "deleteNekndEmployeeApplicationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除公司和学校的员工申请记录\r\n\r\n @param id 公司和学校的员工申请记录主键\r\n @return 结果\r\n"}, {"name": "deleteNekndEmployeeApplicationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除公司和学校的员工申请记录\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}, {"name": "selectNekndEmployeeApplicationByIdCount", "paramTypes": ["java.lang.Long"], "doc": "\n 查询对应id记录数\r\n\r\n @return 【记录数】\r\n"}, {"name": "updateEmployeeApplicationReviewStatus", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": "\n 更新员工申请审核状态\r\n\r\n @param deptId 部门ID\r\n @param userId 用户ID\r\n @param reviewStatus 审核状态\r\n @param status 申请类型\r\n @return 结果\r\n"}], "constructors": []}