package org.dromara.workflow.domain;

import io.github.linpeilie.AutoMapperConfig__961;
import io.github.linpeilie.BaseMapper;
import org.dromara.workflow.domain.bo.TestLeaveBoToTestLeaveMapper__14;
import org.dromara.workflow.domain.vo.TestLeaveVo;
import org.dromara.workflow.domain.vo.TestLeaveVoToTestLeaveMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__961.class,
    uses = {TestLeaveVoToTestLeaveMapper__14.class,TestLeaveBoToTestLeaveMapper__14.class},
    imports = {}
)
public interface TestLeaveToTestLeaveVoMapper__14 extends BaseMapper<TestLeave, TestLeaveVo> {
}
