{"doc": "\n 引产入校信息对象 neknd_induction_school\r\n\r\n <AUTHOR>\r\n @date 2024-12-26\r\n", "fields": [{"name": "id", "doc": "引产入校ID "}, {"name": "cover<PERSON>ri", "doc": "封面图 "}, {"name": "title", "doc": "标题 "}, {"name": "info", "doc": "简介 "}, {"name": "delFlag", "doc": "删除标志（0代表存在 2代表删除） "}, {"name": "model", "doc": "合作模式 "}, {"name": "title_a", "doc": "标题1 "}, {"name": "serverConcent", "doc": "服务中心内容 "}, {"name": "title_b", "doc": "标题2 "}, {"name": "operationConcent", "doc": "运营部内容 "}, {"name": "title_c", "doc": "标题3 "}, {"name": "experienceConcent", "doc": "项目经历内容 "}, {"name": "schoolDeptId", "doc": "所属学校id "}, {"name": "collegeName", "doc": "所属二级学院 "}, {"name": "cooperationType", "doc": "所属二级学院 "}, {"name": "collaborator", "doc": "合作方 "}], "enumConstants": [], "methods": [], "constructors": []}