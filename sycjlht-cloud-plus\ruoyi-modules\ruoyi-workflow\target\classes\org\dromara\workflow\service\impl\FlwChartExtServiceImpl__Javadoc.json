{"doc": " 流程图提示信息\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "execute", "paramTypes": ["org.dromara.warm.flow.core.dto.DefJson"], "doc": " 设置流程图提示信息\n\n @param defJson 流程定义json对象\n"}, {"name": "initPrompt<PERSON><PERSON>nt", "paramTypes": ["org.dromara.warm.flow.core.dto.DefJson"], "doc": " 初始化流程图提示信息\n\n @param defJson 流程定义json对象\n"}, {"name": "processNodeExtInfo", "paramTypes": ["org.dromara.warm.flow.core.dto.NodeJson", "java.util.List", "java.util.Map", "java.util.Map"], "doc": " 处理节点的扩展信息，构建用于流程图悬浮提示的内容\n\n @param nodeJson 当前节点对象\n @param taskList 当前节点对应的历史审批任务列表\n"}, {"name": "buildInfoItem", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 构建单条提示内容对象 InfoItem，用于悬浮窗显示（key: value）\n\n @param key   字段名（作为前缀）\n @param value 字段值\n @return 提示项对象\n"}, {"name": "getHisTaskGroupedByNode", "paramTypes": ["java.lang.Long"], "doc": " 根据流程实例ID获取历史任务列表\n\n @param instanceId 流程实例ID\n @return 历史任务列表\n"}], "constructors": []}