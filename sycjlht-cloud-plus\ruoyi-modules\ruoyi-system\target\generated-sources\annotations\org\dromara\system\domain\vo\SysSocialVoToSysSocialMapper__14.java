package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__960;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysSocial;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__960.class,
    uses = {SysSocialToSysSocialVoMapper__14.class},
    imports = {}
)
public interface SysSocialVoToSysSocialMapper__14 extends BaseMapper<SysSocialVo, SysSocial> {
}
