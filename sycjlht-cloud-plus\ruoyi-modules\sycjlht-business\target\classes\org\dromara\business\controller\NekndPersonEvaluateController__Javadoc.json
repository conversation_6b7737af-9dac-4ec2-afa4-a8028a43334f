{"doc": "\n 企业/学校评价Controller\r\n \r\n <AUTHOR>\r\n @date 2024-05-11\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询企业/学校评价列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndPersonEvaluate"], "doc": "\n 导出企业/学校评价列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取企业/学校评价详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate"], "doc": "\n 新增企业/学校评价\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate"], "doc": "\n 修改企业/学校评价\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除企业/学校评价\r\n"}, {"name": "deptquery", "paramTypes": [], "doc": "\n 获取当前企业的人员列表\r\n"}, {"name": "deptquerySchool", "paramTypes": [], "doc": "\n 获取当前学校的人员列表\r\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询个人职业生涯/查询学校或企业评价列表\r\n"}, {"name": "getUserEvaluateStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户的评价统计\r\n"}, {"name": "getRecentEvaluates", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取最近的评价记录\r\n"}], "constructors": []}