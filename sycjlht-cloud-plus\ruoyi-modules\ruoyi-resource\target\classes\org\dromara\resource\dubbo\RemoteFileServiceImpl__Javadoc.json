{"doc": "\n 文件请求处理\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "upload", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "byte[]"], "doc": "\n 文件上传请求\r\n"}, {"name": "selectUrlByIds", "paramTypes": ["java.lang.String"], "doc": "\n 通过ossId查询对应的url\r\n\r\n @param ossIds ossId串逗号分隔\r\n @return url串逗号分隔\r\n"}, {"name": "selectByIds", "paramTypes": ["java.lang.String"], "doc": "\n 通过ossId查询列表\r\n\r\n @param ossIds ossId串逗号分隔\r\n @return 列表\r\n"}], "constructors": []}