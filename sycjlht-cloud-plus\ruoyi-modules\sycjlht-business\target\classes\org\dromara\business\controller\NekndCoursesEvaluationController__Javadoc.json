{"doc": "\n 课程评价记录Controller\r\n \r\n <AUTHOR>\r\n @date 2024-12-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCoursesEvaluation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询课程评价记录列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCoursesEvaluation"], "doc": "\n 导出课程评价记录列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取课程评价记录详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCoursesEvaluation"], "doc": "\n 新增课程评价记录\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCoursesEvaluation"], "doc": "\n 修改课程评价记录\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除课程评价记录\r\n"}], "constructors": []}