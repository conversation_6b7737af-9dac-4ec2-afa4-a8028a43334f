{"doc": "\n 【请填写功能名称】Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-12-23\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryByIndustryId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询【请填写功能名称】\r\n\r\n @param industryId 【请填写功能名称】主键\r\n @return 【请填写功能名称】\r\n"}, {"name": "selectNekndIndustryList", "paramTypes": ["org.dromara.business.domain.NekndIndustry"], "doc": "\n 查询【请填写功能名称】列表\r\n\r\n @param nekndIndustry 【请填写功能名称】\r\n @return 【请填写功能名称】集合\r\n"}, {"name": "insertNekndIndustry", "paramTypes": ["org.dromara.business.domain.NekndIndustry"], "doc": "\n 新增【请填写功能名称】\r\n\r\n @param nekndIndustry 【请填写功能名称】\r\n @return 结果\r\n"}, {"name": "updateNekndIndustry", "paramTypes": ["org.dromara.business.domain.NekndIndustry"], "doc": "\n 修改【请填写功能名称】\r\n\r\n @param nekndIndustry 【请填写功能名称】\r\n @return 结果\r\n"}, {"name": "deleteNekndIndustryByIndustryId", "paramTypes": ["java.lang.Long"], "doc": "\n 删除【请填写功能名称】\r\n\r\n @param industryId 【请填写功能名称】主键\r\n @return 结果\r\n"}, {"name": "deleteNekndIndustryByIndustryIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除【请填写功能名称】\r\n\r\n @param industryIds 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}