{"doc": "\n 数字人才画像Controller\r\n \r\n <AUTHOR>\r\n @date 2024-11-11\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询数字人才画像列表\r\n"}, {"name": "lists", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询数字人才画像列表（带详细信息）\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndPortraitTalent"], "doc": "\n 导出数字人才画像列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取数字人才画像详细信息\r\n"}, {"name": "getInfoById", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取数字人才画像详细Info\r\n"}, {"name": "getArrayById", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取数字人才画像战力数字的数组\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent"], "doc": "\n 新增数字人才画像\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent"], "doc": "\n 修改数字人才画像\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除数字人才画像\r\n"}], "constructors": []}