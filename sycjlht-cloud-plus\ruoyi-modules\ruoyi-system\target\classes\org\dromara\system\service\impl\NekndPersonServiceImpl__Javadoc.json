{"doc": "\n 个人信息扩展Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-06-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询个人信息扩展\r\n"}, {"name": "queryByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询个人信息\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.NekndPersonBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询个人信息扩展列表\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.NekndPersonBo"], "doc": "\n 查询个人信息扩展列表\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.NekndPersonBo"], "doc": "\n 新增个人信息扩展\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.NekndPersonBo"], "doc": "\n 修改个人信息扩展\r\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.system.domain.NekndPerson"], "doc": "\n 保存前的数据校验\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": "\n 批量删除个人信息扩展\r\n"}, {"name": "queryByCompanyDeptId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据企业部门ID查询个人列表\r\n"}, {"name": "queryBySchoolDeptId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据学校部门ID查询个人列表\r\n"}, {"name": "updatePersonStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 更新个人信息状态\r\n"}, {"name": "updatePersonClassify", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 更新个人信息分类\r\n"}, {"name": "getClassifyStatistics", "paramTypes": [], "doc": "\n 获取分类统计\r\n"}, {"name": "getEmploymentStatistics", "paramTypes": [], "doc": "\n 获取就业状态统计\r\n"}, {"name": "getTalentsAndExpertsCount", "paramTypes": [], "doc": "\n 获取人才和专家数量统计\r\n"}, {"name": "getTalentCategoriesCount", "paramTypes": [], "doc": "\n 获取人才类别数量统计\r\n"}, {"name": "registerBind", "paramTypes": ["org.dromara.system.domain.NekndPerson"], "doc": "\n 对接咸宁数据，注册绑定成宁的账号\r\n"}, {"name": "selectMaxTop", "paramTypes": ["java.lang.Long"], "doc": "\n 获取当前最大置顶值（排除指定ID）\r\n"}, {"name": "queryRecommendedPageList", "paramTypes": ["org.dromara.system.domain.bo.NekndPersonBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询推荐人员列表\r\n 基于单体系统selectRecommendedList的推荐算法逻辑\r\n"}, {"name": "selectNekndPersonByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询人员信息\r\n \r\n @param userId 用户ID\r\n @return 人员信息\r\n"}, {"name": "selectNekndPersonList", "paramTypes": ["org.dromara.system.domain.NekndPerson"], "doc": "\n 查询人员信息列表\r\n \r\n @param nekndPerson 查询条件\r\n @return 人员信息列表\r\n"}, {"name": "updateNeknd<PERSON>erson", "paramTypes": ["org.dromara.system.domain.NekndPerson"], "doc": "\n 更新人员信息\r\n \r\n @param nekndPerson 人员信息\r\n @return 更新结果\r\n"}, {"name": "saveOrUpdateNekndPerson", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 更新或保存个人简历信息（AI功能）\r\n"}, {"name": "saveOrUpdateNekndResumeApp", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 更新或保存个人简历应用信息（AI功能）\r\n"}, {"name": "updateAppointment", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 更新专家预约信息\r\n"}, {"name": "getAppointment", "paramTypes": ["java.lang.Long"], "doc": "\n 获取专家预约信息\r\n"}, {"name": "updateTheme", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 更新用户主题设置\r\n"}, {"name": "talentShowcase", "paramTypes": [], "doc": "\n 人才展示列表（用于首页展示）\r\n"}, {"name": "queryPageListWithDynamicSort", "paramTypes": ["org.dromara.system.domain.bo.NekndPersonBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询个人信息列表（支持动态排序）\r\n 基于单体系统的sortMoney和sortWorkYear排序逻辑\r\n"}, {"name": "queryListWithDynamicSort", "paramTypes": ["org.dromara.system.domain.bo.NekndPersonBo"], "doc": "\n 查询个人信息列表（支持动态排序）\r\n"}, {"name": "queryDataQualityIssueList", "paramTypes": ["org.dromara.system.domain.bo.NekndPersonBo"], "doc": "\n 查询不符合数据质量标准的记录（用于管理员数据清理）\r\n 基于单体系统的JSON验证逻辑，找出数据不完整的记录\r\n"}, {"name": "countDataQualityIssues", "paramTypes": [], "doc": "\n 统计数据质量问题数量\r\n"}], "constructors": []}