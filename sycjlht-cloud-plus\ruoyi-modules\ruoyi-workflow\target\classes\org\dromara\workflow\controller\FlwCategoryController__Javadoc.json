{"doc": "\n 流程分类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": "\n 查询流程分类列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出流程分类列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取流程分类详细信息\r\n\r\n @param categoryId 主键\r\n"}, {"name": "add", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": "\n 新增流程分类\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": "\n 修改流程分类\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long"], "doc": "\n 删除流程分类\r\n\r\n @param categoryId 主键\r\n"}, {"name": "categoryTree", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": "\n 获取流程分类树列表\r\n\r\n @param categoryBo 流程分类\r\n"}], "constructors": []}