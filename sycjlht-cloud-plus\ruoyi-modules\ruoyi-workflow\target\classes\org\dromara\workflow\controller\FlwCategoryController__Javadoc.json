{"doc": " 流程分类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": " 查询流程分类列表\n"}, {"name": "export", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出流程分类列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取流程分类详细信息\n\n @param categoryId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": " 新增流程分类\n"}, {"name": "edit", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": " 修改流程分类\n"}, {"name": "remove", "paramTypes": ["java.lang.Long"], "doc": " 删除流程分类\n\n @param categoryId 主键\n"}, {"name": "categoryTree", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": " 获取流程分类树列表\n\n @param categoryBo 流程分类\n"}], "constructors": []}