{"doc": "\n 报告管理Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-09-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryKnowledgeBaseById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询报告管理\r\n\r\n @param id 报告管理主键\r\n @return 报告管理\r\n"}, {"name": "selectNekndIndustryKnowledgeBaseList", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase"], "doc": "\n 查询报告管理列表\r\n\r\n @param nekndIndustryKnowledgeBase 报告管理\r\n @return 报告管理\r\n"}, {"name": "insertNekndIndustryKnowledgeBase", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase"], "doc": "\n 新增报告管理\r\n\r\n @param nekndIndustryKnowledgeBase 报告管理\r\n @return 结果\r\n"}, {"name": "updateNekndIndustryKnowledgeBase", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase"], "doc": "\n 修改报告管理\r\n\r\n @param nekndIndustryKnowledgeBase 报告管理\r\n @return 结果\r\n"}, {"name": "deleteNekndIndustryKnowledgeBaseByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除报告管理\r\n\r\n @param ids 需要删除的报告管理主键\r\n @return 结果\r\n"}, {"name": "deleteNekndIndustryKnowledgeBaseById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除报告管理信息\r\n\r\n @param id 报告管理主键\r\n @return 结果\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询知识库列表\r\n"}, {"name": "checkUserAccess", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 检查用户对知识库的访问权限\r\n"}, {"name": "queryPopularKnowledgeBase", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询热门知识库\r\n"}], "constructors": []}