{"doc": "\n 图表工具类\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "SEPARATOR", "doc": "\n 分隔符\r\n"}], "enumConstants": [], "methods": [{"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": "\n 判断 key是否存在\r\n\r\n @param key 键\r\n @return true 存在 false不存在\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 设置字典缓存\r\n\r\n @param key        参数键\r\n @param chartDatas 字典数据列表\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": "\n 获取字典缓存\r\n\r\n @param key 参数键\r\n @return chartDatas 字典数据列表\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": "\n 删除指定字典缓存\r\n\r\n @param key 字典键\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": "\n 清空字典缓存\r\n"}, {"name": "get<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": "\n 设置cache key\r\n\r\n @param configKey 参数键\r\n @return 缓存键key\r\n"}], "constructors": []}