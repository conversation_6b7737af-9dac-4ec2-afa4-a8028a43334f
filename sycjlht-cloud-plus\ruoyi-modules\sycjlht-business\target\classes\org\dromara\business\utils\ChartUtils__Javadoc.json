{"doc": " 图表工具类\n\n <AUTHOR>\n", "fields": [{"name": "SEPARATOR", "doc": " 分隔符\n"}], "enumConstants": [], "methods": [{"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 判断 key是否存在\n\n @param key 键\n @return true 存在 false不存在\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 设置字典缓存\n\n @param key        参数键\n @param chartDatas 字典数据列表\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 获取字典缓存\n\n @param key 参数键\n @return chartDatas 字典数据列表\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 删除指定字典缓存\n\n @param key 字典键\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": " 清空字典缓存\n"}, {"name": "get<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 设置cache key\n\n @param configKey 参数键\n @return 缓存键key\n"}], "constructors": []}