{"doc": "\n 专业Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-09-05\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSpecialtyList", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": "\n 查询专业列表\r\n\r\n @param nekndSpecialty 专业\r\n @return 专业集合\r\n"}, {"name": "selectNekndSpecialtyById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询专业\r\n\r\n @param id 专业主键\r\n @return 专业\r\n"}, {"name": "insertNekndSpecialty", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": "\n 新增专业\r\n\r\n @param nekndSpecialty 专业\r\n @return 结果\r\n"}, {"name": "updateNekndSpecialty", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": "\n 修改专业\r\n\r\n @param nekndSpecialty 专业\r\n @return 结果\r\n"}, {"name": "deleteNekndSpecialtyById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除专业\r\n\r\n @param id 专业主键\r\n @return 结果\r\n"}, {"name": "deleteNekndSpecialtyByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除专业\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}