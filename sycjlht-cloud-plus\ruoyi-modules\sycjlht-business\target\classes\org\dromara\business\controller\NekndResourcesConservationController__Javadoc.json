{"doc": "\n 资源共建（校企合作）Controller\r\n \r\n <AUTHOR>\r\n @date 2024-11-06\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": "\n 查询资源共建（校企合作）列表\r\n"}, {"name": "listPage", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": "\n 查询资源共建（校企合作）列表（分页）\r\n"}, {"name": "listCompany", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": "\n 查询资源共建（校企合作）企业列表\r\n"}, {"name": "listGovernment", "paramTypes": [], "doc": "\n 查询资源共建（校企合作）政府列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndResourcesConservation"], "doc": "\n 导出资源共建（校企合作）列表\r\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 导入资源共建信息\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取资源共建（校企合作）详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": "\n 新增资源共建（校企合作）\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": "\n 修改资源共建（校企合作）\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除资源共建（校企合作）\r\n"}, {"name": "getList", "paramTypes": [], "doc": "\n 根据部门ID获取资源共建统计列表\r\n"}, {"name": "getSortList", "paramTypes": [], "doc": "\n 根据部门ID获取资源共建排序列表\r\n"}], "constructors": []}