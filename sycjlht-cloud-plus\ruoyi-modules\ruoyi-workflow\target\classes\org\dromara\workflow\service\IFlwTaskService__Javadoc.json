{"doc": "\n 任务 服务层\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "startWorkFlow", "paramTypes": ["org.dromara.workflow.domain.bo.StartProcessBo"], "doc": "\n 启动任务\r\n\r\n @param startProcessBo 启动流程参数\r\n @return 结果\r\n"}, {"name": "completeTask", "paramTypes": ["org.dromara.workflow.domain.bo.CompleteTaskBo"], "doc": "\n 办理任务\r\n\r\n @param completeTaskBo 办理任务参数\r\n @return 结果\r\n"}, {"name": "setCopy", "paramTypes": ["org.dromara.warm.flow.core.entity.Task", "java.util.List"], "doc": "\n 添加抄送人\r\n\r\n @param task         任务信息\r\n @param flowCopyList 抄送人\r\n"}, {"name": "pageByTaskWait", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询当前用户的待办任务\r\n\r\n @param flowTaskBo 参数\r\n @param pageQuery  分页\r\n @return 结果\r\n"}, {"name": "pageByTaskFinish", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询当前租户所有待办任务\r\n\r\n @param flowTaskBo 参数\r\n @param pageQuery  分页\r\n @return 结果\r\n"}, {"name": "pageByAllTaskWait", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询待办任务\r\n\r\n @param flowTaskBo 参数\r\n @param pageQuery  分页\r\n @return 结果\r\n"}, {"name": "pageByAllTaskFinish", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询已办任务\r\n\r\n @param flowTaskBo 参数\r\n @param pageQuery  分页\r\n @return 结果\r\n"}, {"name": "pageByTaskCopy", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询当前用户的抄送\r\n\r\n @param flowTaskBo 参数\r\n @param pageQuery  分页\r\n @return 结果\r\n"}, {"name": "updateAssignee", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 修改任务办理人\r\n\r\n @param taskIdList 任务id\r\n @param userId     用户id\r\n @return 结果\r\n"}, {"name": "backProcess", "paramTypes": ["org.dromara.workflow.domain.bo.BackProcessBo"], "doc": "\n 驳回审批\r\n\r\n @param bo 参数\r\n @return 结果\r\n"}, {"name": "getBackTaskNode", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 获取可驳回的前置节点\r\n\r\n @param definitionId 流程定义id\r\n @param nowNodeCode  当前节点\r\n @return 结果\r\n"}, {"name": "terminationTask", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTerminationBo"], "doc": "\n 终止任务\r\n\r\n @param bo 参数\r\n @return 结果\r\n"}, {"name": "selectByIdList", "paramTypes": ["java.util.List"], "doc": "\n 按照任务id查询任务\r\n\r\n @param taskIdList 任务id\r\n @return 结果\r\n"}, {"name": "selectById", "paramTypes": ["java.lang.Long"], "doc": "\n 按照任务id查询任务\r\n\r\n @param taskId 任务id\r\n @return 结果\r\n"}, {"name": "getNextNodeList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowNextNodeBo"], "doc": "\n 获取下一节点信息\r\n\r\n @param bo 参数\r\n @return 结果\r\n"}, {"name": "selectHisTaskByIdList", "paramTypes": ["java.util.List"], "doc": "\n 按照任务id查询任务\r\n\r\n @param taskIdList 任务id\r\n @return 结果\r\n"}, {"name": "selectHisTaskById", "paramTypes": ["java.lang.Long"], "doc": "\n 按照任务id查询任务\r\n\r\n @param taskId 任务id\r\n @return 结果\r\n"}, {"name": "selectByInstIdList", "paramTypes": ["java.util.List"], "doc": "\n 按照实例id查询任务\r\n\r\n @param instanceIdList 流程实例id\r\n @return 结果\r\n"}, {"name": "selectByInstId", "paramTypes": ["java.lang.Long"], "doc": "\n 按照实例id查询任务\r\n\r\n @param instanceId 流程实例id\r\n @return 结果\r\n"}, {"name": "taskOperation", "paramTypes": ["org.dromara.workflow.domain.bo.TaskOperationBo", "java.lang.String"], "doc": "\n 任务操作\r\n\r\n @param bo            参数\r\n @param taskOperation 操作类型，委派 delegateTask、转办 transferTask、加签 addSignature、减签 reductionSignature\r\n @return 结果\r\n"}, {"name": "currentTaskAllUser", "paramTypes": ["java.util.List"], "doc": "\n 获取任务所有办理人\r\n\r\n @param taskIdList 任务id\r\n @return 结果\r\n"}, {"name": "currentTaskAllUser", "paramTypes": ["java.lang.Long"], "doc": "\n 获取当前任务的所有办理人\r\n\r\n @param taskId 任务id\r\n @return 结果\r\n"}, {"name": "getByNodeCode", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 按照节点编码查询节点\r\n\r\n @param nodeCode     节点编码\r\n @param definitionId 流程定义id\r\n @return 节点\r\n"}], "constructors": []}