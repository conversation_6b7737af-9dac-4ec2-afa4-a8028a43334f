{"doc": " 任务 服务层\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "startWorkFlow", "paramTypes": ["org.dromara.workflow.domain.bo.StartProcessBo"], "doc": " 启动任务\n\n @param startProcessBo 启动流程参数\n @return 结果\n"}, {"name": "completeTask", "paramTypes": ["org.dromara.workflow.domain.bo.CompleteTaskBo"], "doc": " 办理任务\n\n @param completeTaskBo 办理任务参数\n @return 结果\n"}, {"name": "setCopy", "paramTypes": ["org.dromara.warm.flow.core.entity.Task", "java.util.List"], "doc": " 添加抄送人\n\n @param task         任务信息\n @param flowCopyList 抄送人\n"}, {"name": "pageByTaskWait", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询当前用户的待办任务\n\n @param flowTaskBo 参数\n @param pageQuery  分页\n @return 结果\n"}, {"name": "pageByTaskFinish", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询当前租户所有待办任务\n\n @param flowTaskBo 参数\n @param pageQuery  分页\n @return 结果\n"}, {"name": "pageByAllTaskWait", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询待办任务\n\n @param flowTaskBo 参数\n @param pageQuery  分页\n @return 结果\n"}, {"name": "pageByAllTaskFinish", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询已办任务\n\n @param flowTaskBo 参数\n @param pageQuery  分页\n @return 结果\n"}, {"name": "pageByTaskCopy", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询当前用户的抄送\n\n @param flowTaskBo 参数\n @param pageQuery  分页\n @return 结果\n"}, {"name": "updateAssignee", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 修改任务办理人\n\n @param taskIdList 任务id\n @param userId     用户id\n @return 结果\n"}, {"name": "backProcess", "paramTypes": ["org.dromara.workflow.domain.bo.BackProcessBo"], "doc": " 驳回审批\n\n @param bo 参数\n @return 结果\n"}, {"name": "getBackTaskNode", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 获取可驳回的前置节点\n\n @param definitionId 流程定义id\n @param nowNodeCode  当前节点\n @return 结果\n"}, {"name": "terminationTask", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTerminationBo"], "doc": " 终止任务\n\n @param bo 参数\n @return 结果\n"}, {"name": "selectByIdList", "paramTypes": ["java.util.List"], "doc": " 按照任务id查询任务\n\n @param taskIdList 任务id\n @return 结果\n"}, {"name": "selectById", "paramTypes": ["java.lang.Long"], "doc": " 按照任务id查询任务\n\n @param taskId 任务id\n @return 结果\n"}, {"name": "getNextNodeList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowNextNodeBo"], "doc": " 获取下一节点信息\n\n @param bo 参数\n @return 结果\n"}, {"name": "selectHisTaskByIdList", "paramTypes": ["java.util.List"], "doc": " 按照任务id查询任务\n\n @param taskIdList 任务id\n @return 结果\n"}, {"name": "selectHisTaskById", "paramTypes": ["java.lang.Long"], "doc": " 按照任务id查询任务\n\n @param taskId 任务id\n @return 结果\n"}, {"name": "selectByInstIdList", "paramTypes": ["java.util.List"], "doc": " 按照实例id查询任务\n\n @param instanceIdList 流程实例id\n @return 结果\n"}, {"name": "selectByInstId", "paramTypes": ["java.lang.Long"], "doc": " 按照实例id查询任务\n\n @param instanceId 流程实例id\n @return 结果\n"}, {"name": "taskOperation", "paramTypes": ["org.dromara.workflow.domain.bo.TaskOperationBo", "java.lang.String"], "doc": " 任务操作\n\n @param bo            参数\n @param taskOperation 操作类型，委派 delegateTask、转办 transferTask、加签 addSignature、减签 reductionSignature\n @return 结果\n"}, {"name": "currentTaskAllUser", "paramTypes": ["java.util.List"], "doc": " 获取任务所有办理人\n\n @param taskIdList 任务id\n @return 结果\n"}, {"name": "currentTaskAllUser", "paramTypes": ["java.lang.Long"], "doc": " 获取当前任务的所有办理人\n\n @param taskId 任务id\n @return 结果\n"}, {"name": "getByNodeCode", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 按照节点编码查询节点\n\n @param nodeCode     节点编码\n @param definitionId 流程定义id\n @return 节点\n"}], "constructors": []}