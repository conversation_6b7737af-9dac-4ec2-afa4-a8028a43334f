{"doc": "\n 角色服务远程实现\r\n\r\n <AUTHOR>\r\n @date 2025-08-02\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getRoleIdsByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID获取角色列表\r\n \r\n @param userId 用户ID\r\n @return 角色ID集合\r\n"}, {"name": "getRoleKeysByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID获取角色权限字符串列表\r\n \r\n @param userId 用户ID\r\n @return 角色权限字符串集合\r\n"}, {"name": "hasRole", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 检查用户是否拥有某个角色\r\n \r\n @param userId 用户ID\r\n @param roleKey 角色权限字符串\r\n @return 是否拥有\r\n"}, {"name": "isStudent", "paramTypes": ["java.lang.Long"], "doc": "\n 检查用户是否为学生角色\r\n \r\n @param userId 用户ID\r\n @return 是否学生\r\n"}, {"name": "<PERSON><PERSON><PERSON>er", "paramTypes": ["java.lang.Long"], "doc": "\n 检查用户是否为教师角色\r\n \r\n @param userId 用户ID\r\n @return 是否教师\r\n"}, {"name": "isCompanyUser", "paramTypes": ["java.lang.Long"], "doc": "\n 检查用户是否为企业角色\r\n \r\n @param userId 用户ID\r\n @return 是否企业用户\r\n"}, {"name": "selectRoleListByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID获取角色ID列表\r\n \r\n @param userId 用户ID\r\n @return 角色ID列表\r\n"}], "constructors": []}