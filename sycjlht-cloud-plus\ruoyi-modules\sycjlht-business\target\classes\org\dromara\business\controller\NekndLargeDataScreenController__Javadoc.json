{"doc": "\n 数据大屏Controller\r\n \r\n <AUTHOR>\r\n @date 2025-01-27\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getInfo", "paramTypes": ["java.lang.String"], "doc": "\n 获取数据大屏信息（公开接口）\r\n"}, {"name": "list", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询数据大屏列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndLargeDataScreen"], "doc": "\n 导出数据大屏列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取数据大屏详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": "\n 新增数据大屏\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": "\n 修改数据大屏\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除数据大屏\r\n"}, {"name": "getRealtimeData", "paramTypes": ["java.lang.String"], "doc": "\n 获取实时数据大屏数据\r\n"}, {"name": "refreshScreenData", "paramTypes": ["java.lang.String"], "doc": "\n 异步刷新数据大屏数据\r\n"}, {"name": "getScreenConfig", "paramTypes": ["java.lang.String"], "doc": "\n 获取数据大屏配置\r\n"}], "constructors": []}