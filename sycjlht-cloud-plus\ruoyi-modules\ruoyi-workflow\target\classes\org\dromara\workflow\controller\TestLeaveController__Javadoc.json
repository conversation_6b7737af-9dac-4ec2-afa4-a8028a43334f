{"doc": "\n 请假\r\n\r\n <AUTHOR>\r\n @date 2023-07-21\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询请假列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出请假列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取请假详细信息\r\n\r\n @param id 主键\r\n"}, {"name": "add", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": "\n 新增请假\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": "\n 修改请假\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除请假\r\n\r\n @param ids 主键串\r\n"}], "constructors": []}