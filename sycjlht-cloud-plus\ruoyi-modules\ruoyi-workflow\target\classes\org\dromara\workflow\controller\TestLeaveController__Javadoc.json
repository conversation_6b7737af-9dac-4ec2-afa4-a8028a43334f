{"doc": " 请假\n\n <AUTHOR>\n @date 2023-07-21\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询请假列表\n"}, {"name": "export", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出请假列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取请假详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": " 新增请假\n"}, {"name": "edit", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": " 修改请假\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除请假\n\n @param ids 主键串\n"}], "constructors": []}