package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__960;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysTenant;
import org.dromara.system.domain.SysTenantToSysTenantVoMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__960.class,
    uses = {SysTenantToSysTenantVoMapper__14.class},
    imports = {}
)
public interface SysTenantVoToSysTenantMapper__14 extends BaseMapper<SysTenantVo, SysTenant> {
}
