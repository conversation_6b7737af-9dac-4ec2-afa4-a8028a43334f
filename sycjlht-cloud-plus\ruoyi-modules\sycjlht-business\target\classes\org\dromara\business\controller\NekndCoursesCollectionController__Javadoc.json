{"doc": "\n 课程收藏记录Controller\r\n \r\n <AUTHOR>\r\n @date 2024-12-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询课程收藏记录列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCoursesCollection"], "doc": "\n 导出课程收藏记录列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取课程收藏记录详细信息\r\n"}, {"name": "collectionsAndUncollections", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection"], "doc": "\n 新增课程收藏记录\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除课程收藏记录\r\n"}, {"name": "getOwnList", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询当前用户收藏课程记录列表\r\n"}], "constructors": []}