{"doc": "\n 成员单位信息Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-10-22\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndUnitById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询成员单位信息\r\n\r\n @param id 成员单位信息主键\r\n @return 成员单位信息\r\n"}, {"name": "selectNekndUnitList", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": "\n 查询成员单位信息列表\r\n\r\n @param nekndUnit 成员单位信息\r\n @return 成员单位信息\r\n"}, {"name": "selectPageNekndUnitList", "paramTypes": ["org.dromara.business.domain.NekndUnit", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询成员单位信息列表\r\n\r\n @param nekndUnit 成员单位信息\r\n @param pageQuery 分页查询参数\r\n @return 分页数据\r\n"}, {"name": "buildQueryWrapper", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": "\n 构建查询条件\r\n"}, {"name": "insertNekndUnit", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": "\n 新增成员单位信息\r\n\r\n @param nekndUnit 成员单位信息\r\n @return 结果\r\n"}, {"name": "updateNekndUnit", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": "\n 修改成员单位信息\r\n\r\n @param nekndUnit 成员单位信息\r\n @return 结果\r\n"}, {"name": "deleteNekndUnitByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除成员单位信息\r\n\r\n @param ids 需要删除的成员单位信息主键\r\n @return 结果\r\n"}, {"name": "deleteNekndUnitById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除成员单位信息信息\r\n\r\n @param id 成员单位信息主键\r\n @return 结果\r\n"}, {"name": "processJsonField", "paramTypes": ["java.util.HashMap", "java.lang.String", "java.lang.String", "com.fasterxml.jackson.databind.ObjectMapper"], "doc": "\n 通用处理方法：解析 JSON 字符串字段，并计算所有 percentage 的平均值\r\n"}, {"name": "getIndustrialParkData", "paramTypes": ["java.lang.String"], "doc": "\n 检测平台-园区数据\r\n \r\n @param name\r\n @return\r\n"}, {"name": "getIndustrialCityData", "paramTypes": ["java.lang.String"], "doc": "\n 检测平台-市数据\r\n \r\n @param name\r\n @return\r\n"}, {"name": "getIndustrialProvincialData", "paramTypes": ["java.lang.String"], "doc": "\n 检测平台-省数据\r\n \r\n @param name\r\n @return\r\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 批量更新单位状态\r\n\r\n @param ids    单位ID列表\r\n @param status 目标状态\r\n @return 更新结果\r\n"}, {"name": "getUnitStatistics", "paramTypes": [], "doc": "\n 获取单位统计信息\r\n\r\n @return 统计数据\r\n"}], "constructors": []}