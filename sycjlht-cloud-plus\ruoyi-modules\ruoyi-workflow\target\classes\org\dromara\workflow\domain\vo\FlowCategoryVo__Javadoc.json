{"doc": "\n 流程分类视图对象 wf_category\r\n\r\n <AUTHOR>\r\n @date 2023-06-27\r\n", "fields": [{"name": "categoryId", "doc": "\n 流程分类ID\r\n"}, {"name": "parentId", "doc": "\n 父级id\r\n"}, {"name": "parentName", "doc": "\n 父类别名称\r\n"}, {"name": "ancestors", "doc": "\n 祖级列表\r\n"}, {"name": "categoryName", "doc": "\n 流程分类名称\r\n"}, {"name": "orderNum", "doc": "\n 显示顺序\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "children", "doc": "\n 子菜单\r\n"}], "enumConstants": [], "methods": [], "constructors": []}