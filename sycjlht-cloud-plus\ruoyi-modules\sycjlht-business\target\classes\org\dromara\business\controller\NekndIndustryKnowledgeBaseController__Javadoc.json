{"doc": "\n 行业知识库管理Controller\r\n \r\n <AUTHOR>\r\n @date 2024-09-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询行业知识库管理列表\r\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 门户查询行业知识库列表（不显示文件链接）\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndIndustryKnowledgeBase"], "doc": "\n 导出行业知识库管理列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取行业知识库详细信息（带权限检查）\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase"], "doc": "\n 新增行业知识库管理\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase"], "doc": "\n 修改行业知识库管理\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除行业知识库管理\r\n"}, {"name": "checkAccess", "paramTypes": ["java.lang.Integer"], "doc": "\n 检查当前用户对指定知识库的访问权限\r\n"}, {"name": "getByCategory", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 按分类查询知识库\r\n"}, {"name": "getPopular", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取热门知识库\r\n"}], "constructors": []}