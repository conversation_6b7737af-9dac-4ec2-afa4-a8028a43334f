package org.dromara.system.domain.convert;

import javax.annotation.processing.Generated;
import org.dromara.system.api.domain.vo.RemoteUserVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T22:52:05+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class SysUserVoConvertImpl implements SysUserVoConvert {

    @Override
    public RemoteUserVo convert(SysUserVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        RemoteUserVo remoteUserVo = new RemoteUserVo();

        remoteUserVo.setCreateTime( arg0.getCreateTime() );
        remoteUserVo.setDeptId( arg0.getDeptId() );
        remoteUserVo.setEmail( arg0.getEmail() );
        remoteUserVo.setNickName( arg0.getNickName() );
        remoteUserVo.setPhonenumber( arg0.getPhonenumber() );
        remoteUserVo.setSex( arg0.getSex() );
        remoteUserVo.setStatus( arg0.getStatus() );
        remoteUserVo.setUserId( arg0.getUserId() );
        remoteUserVo.setUserName( arg0.getUserName() );
        remoteUserVo.setUserType( arg0.getUserType() );

        return remoteUserVo;
    }

    @Override
    public RemoteUserVo convert(SysUserVo arg0, RemoteUserVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setPhonenumber( arg0.getPhonenumber() );
        arg1.setSex( arg0.getSex() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setUserType( arg0.getUserType() );

        return arg1;
    }
}
