{"doc": "\n 字典 业务层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectDictDataList", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo"], "doc": "\n 根据条件分页查询字典数据\r\n\r\n @param dictData 字典数据信息\r\n @return 字典数据集合信息\r\n"}, {"name": "selectDictLabel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典键值查询字典数据信息\r\n\r\n @param dictType  字典类型\r\n @param dictValue 字典键值\r\n @return 字典标签\r\n"}, {"name": "selectDictRemark", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典键值查询字典备注信息\r\n\r\n @param dictType  字典类型\r\n @param dictValue 字典键值\r\n @return 字典备注\r\n"}, {"name": "selectDictDataById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据字典数据ID查询信息\r\n\r\n @param dictCode 字典数据ID\r\n @return 字典数据\r\n"}, {"name": "deleteDictDataByIds", "paramTypes": ["java.util.List"], "doc": "\n 批量删除字典数据信息\r\n\r\n @param dictCodes 需要删除的字典数据ID\r\n"}, {"name": "insertDictData", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo"], "doc": "\n 新增保存字典数据信息\r\n\r\n @param bo 字典数据信息\r\n @return 结果\r\n"}, {"name": "updateDictData", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo"], "doc": "\n 修改保存字典数据信息\r\n\r\n @param bo 字典数据信息\r\n @return 结果\r\n"}, {"name": "checkDictDataUnique", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo"], "doc": "\n 校验字典键值是否唯一\r\n\r\n @param dict 字典数据\r\n @return 结果\r\n"}], "constructors": []}