package org.dromara.system.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.NekndPersonVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:06:27+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Oracle Corporation)"
)
@Component
public class NekndPersonToNekndPersonVoMapperImpl implements NekndPersonToNekndPersonVoMapper {

    @Override
    public NekndPersonVo convert(NekndPerson arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NekndPersonVo nekndPersonVo = new NekndPersonVo();

        nekndPersonVo.setId( arg0.getId() );
        nekndPersonVo.setUserId( arg0.getUserId() );
        nekndPersonVo.setTenantId( arg0.getTenantId() );
        nekndPersonVo.setPictureUri( arg0.getPictureUri() );
        nekndPersonVo.setName( arg0.getName() );
        nekndPersonVo.setSex( arg0.getSex() );
        nekndPersonVo.setAddress( arg0.getAddress() );
        nekndPersonVo.setPhone( arg0.getPhone() );
        nekndPersonVo.setEmail( arg0.getEmail() );
        nekndPersonVo.setWorkYear( arg0.getWorkYear() );
        nekndPersonVo.setPositionStatus( arg0.getPositionStatus() );
        nekndPersonVo.setTechnical( arg0.getTechnical() );
        nekndPersonVo.setExpectedPosition( arg0.getExpectedPosition() );
        nekndPersonVo.setExpectedMoney( arg0.getExpectedMoney() );
        nekndPersonVo.setJobType( arg0.getJobType() );
        nekndPersonVo.setWorkExperienceJson( arg0.getWorkExperienceJson() );
        nekndPersonVo.setEducationalBackgroundJson( arg0.getEducationalBackgroundJson() );
        nekndPersonVo.setProvincialName( arg0.getProvincialName() );
        nekndPersonVo.setCityName( arg0.getCityName() );
        nekndPersonVo.setCompanyDeptName( arg0.getCompanyDeptName() );
        nekndPersonVo.setSchoolDeptName( arg0.getSchoolDeptName() );
        nekndPersonVo.setClassify( arg0.getClassify() );
        nekndPersonVo.setEducationStatus( arg0.getEducationStatus() );
        nekndPersonVo.setProfessionalTitles( arg0.getProfessionalTitles() );
        nekndPersonVo.setExpertsTypes( arg0.getExpertsTypes() );
        nekndPersonVo.setStatus( arg0.getStatus() );
        nekndPersonVo.setAppointment( arg0.getAppointment() );
        nekndPersonVo.setAppointmentTime( arg0.getAppointmentTime() );
        nekndPersonVo.setPersonalExperience( arg0.getPersonalExperience() );
        nekndPersonVo.setResume( arg0.getResume() );
        nekndPersonVo.setCertificateHonorUri( arg0.getCertificateHonorUri() );
        nekndPersonVo.setIsTop( arg0.getIsTop() );
        nekndPersonVo.setUpdateStatus( arg0.getUpdateStatus() );
        if ( arg0.getCreateTime() != null ) {
            nekndPersonVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        if ( arg0.getUpdateTime() != null ) {
            nekndPersonVo.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        nekndPersonVo.setPendingApprovalCompanyDeptId( arg0.getPendingApprovalCompanyDeptId() );
        nekndPersonVo.setPendingApprovalSchoolDeptId( arg0.getPendingApprovalSchoolDeptId() );
        nekndPersonVo.setPendingClassify( arg0.getPendingClassify() );

        return nekndPersonVo;
    }

    @Override
    public NekndPersonVo convert(NekndPerson arg0, NekndPersonVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setTenantId( arg0.getTenantId() );
        arg1.setPictureUri( arg0.getPictureUri() );
        arg1.setName( arg0.getName() );
        arg1.setSex( arg0.getSex() );
        arg1.setAddress( arg0.getAddress() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setWorkYear( arg0.getWorkYear() );
        arg1.setPositionStatus( arg0.getPositionStatus() );
        arg1.setTechnical( arg0.getTechnical() );
        arg1.setExpectedPosition( arg0.getExpectedPosition() );
        arg1.setExpectedMoney( arg0.getExpectedMoney() );
        arg1.setJobType( arg0.getJobType() );
        arg1.setWorkExperienceJson( arg0.getWorkExperienceJson() );
        arg1.setEducationalBackgroundJson( arg0.getEducationalBackgroundJson() );
        arg1.setProvincialName( arg0.getProvincialName() );
        arg1.setCityName( arg0.getCityName() );
        arg1.setCompanyDeptName( arg0.getCompanyDeptName() );
        arg1.setSchoolDeptName( arg0.getSchoolDeptName() );
        arg1.setClassify( arg0.getClassify() );
        arg1.setEducationStatus( arg0.getEducationStatus() );
        arg1.setProfessionalTitles( arg0.getProfessionalTitles() );
        arg1.setExpertsTypes( arg0.getExpertsTypes() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setAppointment( arg0.getAppointment() );
        arg1.setAppointmentTime( arg0.getAppointmentTime() );
        arg1.setPersonalExperience( arg0.getPersonalExperience() );
        arg1.setResume( arg0.getResume() );
        arg1.setCertificateHonorUri( arg0.getCertificateHonorUri() );
        arg1.setIsTop( arg0.getIsTop() );
        arg1.setUpdateStatus( arg0.getUpdateStatus() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setPendingApprovalCompanyDeptId( arg0.getPendingApprovalCompanyDeptId() );
        arg1.setPendingApprovalSchoolDeptId( arg0.getPendingApprovalSchoolDeptId() );
        arg1.setPendingClassify( arg0.getPendingClassify() );

        return arg1;
    }
}
