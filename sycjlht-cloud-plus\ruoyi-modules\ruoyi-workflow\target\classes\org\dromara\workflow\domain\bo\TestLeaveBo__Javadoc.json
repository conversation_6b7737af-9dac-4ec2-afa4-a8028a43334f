{"doc": "\n 请假业务对象 test_leave\r\n\r\n <AUTHOR>\r\n @date 2023-07-21\r\n", "fields": [{"name": "id", "doc": "\n 主键\r\n"}, {"name": "leaveType", "doc": "\n 请假类型\r\n"}, {"name": "startDate", "doc": "\n 开始时间\r\n"}, {"name": "endDate", "doc": "\n 结束时间\r\n"}, {"name": "leaveDays", "doc": "\n 请假天数\r\n"}, {"name": "startLeaveDays", "doc": "\n 开始时间\r\n"}, {"name": "endLeaveDays", "doc": "\n 结束时间\r\n"}, {"name": "remark", "doc": "\n 请假原因\r\n"}, {"name": "status", "doc": "\n 状态\r\n"}], "enumConstants": [], "methods": [], "constructors": []}