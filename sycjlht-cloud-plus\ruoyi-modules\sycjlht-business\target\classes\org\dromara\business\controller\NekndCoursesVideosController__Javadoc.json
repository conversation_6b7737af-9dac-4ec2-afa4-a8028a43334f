{"doc": "\n 课程视频Controller\r\n \r\n <AUTHOR>\r\n @date 2024-12-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideos", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询课程视频列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCoursesVideos"], "doc": "\n 导出课程视频列表\r\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 导入课程视频数据\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取课程视频详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideos"], "doc": "\n 新增课程视频\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideos"], "doc": "\n 修改课程视频\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除课程视频\r\n"}], "constructors": []}