{"doc": "\n 产教融合项目Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-10-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryEducationProjectsById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询产教融合项目\r\n\r\n @param id 产教融合项目主键\r\n @return 产教融合项目\r\n"}, {"name": "selectNekndIndustryEducationProjectsList", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": "\n 查询产教融合项目列表\r\n\r\n @param nekndIndustryEducationProjects 产教融合项目\r\n @return 产教融合项目\r\n"}, {"name": "insertNekndIndustryEducationProjects", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": "\n 新增产教融合项目\r\n\r\n @param nekndIndustryEducationProjects 产教融合项目\r\n @return 结果\r\n"}, {"name": "updateNekndIndustryEducationProjects", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": "\n 修改产教融合项目\r\n\r\n @param nekndIndustryEducationProjects 产教融合项目\r\n @return 结果\r\n"}, {"name": "deleteNekndIndustryEducationProjectsByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除产教融合项目\r\n\r\n @param ids 需要删除的产教融合项目主键\r\n @return 结果\r\n"}, {"name": "deleteNekndIndustryEducationProjectsById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除产教融合项目信息\r\n\r\n @param id 产教融合项目主键\r\n @return 结果\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询产教融合项目列表\r\n"}, {"name": "getProjectStatisticsByType", "paramTypes": [], "doc": "\n 按类型获取项目统计信息\r\n"}, {"name": "getProjectStatisticsByStatus", "paramTypes": [], "doc": "\n 按状态获取项目统计信息\r\n"}, {"name": "queryPopularProjects", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询热门项目\r\n"}], "constructors": []}