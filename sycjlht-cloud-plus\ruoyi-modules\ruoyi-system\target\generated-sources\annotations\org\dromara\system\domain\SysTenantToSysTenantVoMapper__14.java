package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__960;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysTenantBoToSysTenantMapper__14;
import org.dromara.system.domain.vo.SysTenantVo;
import org.dromara.system.domain.vo.SysTenantVoToSysTenantMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__960.class,
    uses = {SysTenantBoToSysTenantMapper__14.class,SysTenantVoToSysTenantMapper__14.class},
    imports = {}
)
public interface SysTenantToSysTenantVoMapper__14 extends BaseMapper<SysTenant, SysTenantVo> {
}
