{"doc": "\n 继续教育信息Controller\r\n \r\n <AUTHOR>\r\n @date 2024-10-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": "\n 查询继续教育信息列表\r\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": "\n 匿名查询继续教育信息列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": "\n 导出继续教育信息列表\r\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 导入继续教育信息\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取继续教育信息详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": "\n 新增继续教育信息\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": "\n 修改继续教育信息\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除继续教育信息\r\n"}], "constructors": []}