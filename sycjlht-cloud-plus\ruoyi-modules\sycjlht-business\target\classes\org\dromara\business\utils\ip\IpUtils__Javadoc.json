{"doc": "\n 获取IP方法\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getIpAddr", "paramTypes": [], "doc": "\n 获取客户端IP\r\n\r\n @return IP地址\r\n"}, {"name": "getIpAddr", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": "\n 获取客户端IP\r\n\r\n @param request 请求对象\r\n @return IP地址\r\n"}, {"name": "internalIp", "paramTypes": ["java.lang.String"], "doc": "\n 检查是否为内部IP地址\r\n\r\n @param ip IP地址\r\n @return 结果\r\n"}, {"name": "internalIp", "paramTypes": ["byte[]"], "doc": "\n 检查是否为内部IP地址\r\n\r\n @param addr byte地址\r\n @return 结果\r\n"}, {"name": "textToNumericFormatV4", "paramTypes": ["java.lang.String"], "doc": "\n 将IPv4地址转换成字节\r\n\r\n @param text IPv4地址\r\n @return byte 字节\r\n"}, {"name": "getHostIp", "paramTypes": [], "doc": "\n 获取IP地址\r\n\r\n @return 本地IP地址\r\n"}, {"name": "getHostName", "paramTypes": [], "doc": "\n 获取主机名\r\n\r\n @return 本地主机名\r\n"}, {"name": "getMultistageReverseProxyIp", "paramTypes": ["java.lang.String"], "doc": "\n 从多级反向代理中获得第一个非unknown IP地址\r\n\r\n @param ip 获得的IP地址\r\n @return 第一个非unknown IP地址\r\n"}, {"name": "isUnknown", "paramTypes": ["java.lang.String"], "doc": "\n 检测给定字符串是否为未知，多用于检测HTTP请求相关\r\n\r\n @param checkString 被检测的字符串\r\n @return 是否未知\r\n"}, {"name": "isIP", "paramTypes": ["java.lang.String"], "doc": "\n 是否为IP\r\n"}, {"name": "isIpWildCard", "paramTypes": ["java.lang.String"], "doc": "\n 是否为IP，或 *为间隔的通配符地址\r\n"}, {"name": "ipIsInWildCardNoCheck", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 检测参数是否在ip通配符里\r\n"}, {"name": "isIPSegment", "paramTypes": ["java.lang.String"], "doc": "\n 是否为特定格式如:“**********-***********”的ip段字符串\r\n"}, {"name": "ipIsInNetNoCheck", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 判断ip是否在指定网段中\r\n"}, {"name": "isMatchedIp", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 校验ip是否符合过滤串规则\r\n\r\n @param filter 过滤IP列表,支持后缀'*'通配,支持网段如:`**********-***********`\r\n @param ip 校验IP地址\r\n @return boolean 结果\r\n"}], "constructors": []}