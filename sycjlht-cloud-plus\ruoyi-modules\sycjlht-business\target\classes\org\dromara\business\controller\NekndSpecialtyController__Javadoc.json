{"doc": "\n 专业信息Controller\r\n \r\n <AUTHOR>\r\n @date 2024-11-06\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndSpecialty", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询专业信息列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndSpecialty"], "doc": "\n 导出专业信息列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取专业信息详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": "\n 新增专业信息\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": "\n 修改专业信息\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除专业信息\r\n"}, {"name": "getByType", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 按专业类型查询\r\n"}, {"name": "search", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 搜索专业信息\r\n"}, {"name": "getPopular", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取热门专业\r\n"}, {"name": "getStatistics", "paramTypes": [], "doc": "\n 获取专业统计信息\r\n"}], "constructors": []}