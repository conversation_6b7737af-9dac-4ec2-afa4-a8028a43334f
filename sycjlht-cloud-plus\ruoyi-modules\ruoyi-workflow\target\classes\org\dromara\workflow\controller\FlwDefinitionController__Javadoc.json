{"doc": " 流程定义管理 控制层\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.warm.flow.orm.entity.FlowDefinition", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询流程定义列表\n\n @param flowDefinition 参数\n @param pageQuery      分页\n"}, {"name": "unPublishList", "paramTypes": ["org.dromara.warm.flow.orm.entity.FlowDefinition", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询未发布的流程定义列表\n\n @param flowDefinition 参数\n @param pageQuery      分页\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取流程定义详细信息\n\n @param id 流程定义id\n"}, {"name": "add", "paramTypes": ["org.dromara.warm.flow.orm.entity.FlowDefinition"], "doc": " 新增流程定义\n\n @param flowDefinition 参数\n"}, {"name": "edit", "paramTypes": ["org.dromara.warm.flow.orm.entity.FlowDefinition"], "doc": " 修改流程定义\n\n @param flowDefinition 参数\n"}, {"name": "publish", "paramTypes": ["java.lang.Long"], "doc": " 发布流程定义\n\n @param id 流程定义id\n"}, {"name": "unPublish", "paramTypes": ["java.lang.Long"], "doc": " 取消发布流程定义\n\n @param id 流程定义id\n"}, {"name": "remove", "paramTypes": ["java.util.List"], "doc": " 删除流程定义\n"}, {"name": "copy", "paramTypes": ["java.lang.Long"], "doc": " 复制流程定义\n\n @param id 流程定义id\n"}, {"name": "importDef", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": " 导入流程定义\n\n @param file     文件\n @param category 分类\n"}, {"name": "exportDef", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出流程定义\n\n @param id       流程定义id\n @param response 响应\n @throws IOException 异常\n"}, {"name": "xmlString", "paramTypes": ["java.lang.Long"], "doc": " 获取流程定义JSON字符串\n\n @param id 流程定义id\n"}, {"name": "active", "paramTypes": ["java.lang.Long", "boolean"], "doc": " 激活/挂起流程定义\n\n @param id     流程定义id\n @param active 激活/挂起\n"}], "constructors": []}