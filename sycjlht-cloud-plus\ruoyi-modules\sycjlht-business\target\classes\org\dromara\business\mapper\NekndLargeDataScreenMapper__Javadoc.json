{"doc": "\n 数据大屏Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-04-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndLargeDataScreenById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询数据大屏\r\n\r\n @param id 数据大屏主键\r\n @return 数据大屏\r\n"}, {"name": "selectNekndLargeDataScreenList", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": "\n 查询数据大屏列表\r\n\r\n @param nekndLargeDataScreen 数据大屏\r\n @return 数据大屏集合\r\n"}, {"name": "insertNekndLargeDataScreen", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": "\n 新增数据大屏\r\n\r\n @param nekndLargeDataScreen 数据大屏\r\n @return 结果\r\n"}, {"name": "updateNekndLargeDataScreen", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": "\n 修改数据大屏\r\n\r\n @param nekndLargeDataScreen 数据大屏\r\n @return 结果\r\n"}, {"name": "deleteNekndLargeDataScreenById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除数据大屏\r\n\r\n @param id 数据大屏主键\r\n @return 结果\r\n"}, {"name": "deleteNekndLargeDataScreenByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除数据大屏\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}