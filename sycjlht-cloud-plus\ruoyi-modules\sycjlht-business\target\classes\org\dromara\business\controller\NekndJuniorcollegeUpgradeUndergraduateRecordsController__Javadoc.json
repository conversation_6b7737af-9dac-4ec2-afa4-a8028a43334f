{"doc": "\n 继续教育报名记录Controller\r\n \r\n <AUTHOR>\r\n @date 2024-10-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": "\n 后台查询继续教育报名记录列表\r\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": "\n 门户查询继续教育报名记录列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": "\n 导出继续教育报名记录列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取继续教育报名记录详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": "\n 新增继续教育报名记录\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": "\n 修改继续教育报名记录\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除继续教育报名记录\r\n"}], "constructors": []}