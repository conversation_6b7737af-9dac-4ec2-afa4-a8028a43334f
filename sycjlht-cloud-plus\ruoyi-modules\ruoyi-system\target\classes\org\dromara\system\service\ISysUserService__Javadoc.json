{"doc": " 用户 业务层\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPageUserList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据条件分页查询用户列表\n\n @param user      用户信息\n @param pageQuery 发呢也\n @return 用户信息\n"}, {"name": "selectUserExportList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": " 导出用户列表\n\n @param user 用户信息\n @return 用户信息集合信息\n"}, {"name": "selectAllocatedList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据条件分页查询已分配用户角色列表\n\n @param user 用户信息\n @return 用户信息集合信息\n"}, {"name": "selectUnallocatedList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据条件分页查询未分配用户角色列表\n\n @param user 用户信息\n @return 用户信息集合信息\n"}, {"name": "selectUserByUserName", "paramTypes": ["java.lang.String"], "doc": " 通过用户名查询用户\n\n @param userName 用户名\n @return 用户对象信息\n"}, {"name": "selectUserByPhonenumber", "paramTypes": ["java.lang.String"], "doc": " 通过手机号查询用户\n\n @param phonenumber 手机号\n @return 用户对象信息\n"}, {"name": "selectUserById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID查询用户\n\n @param userId 用户ID\n @return 用户对象信息\n"}, {"name": "selectUserByIds", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 通过用户ID串查询用户\n\n @param userIds 用户ID串\n @param deptId  部门id\n @return 用户列表信息\n"}, {"name": "selectUserRoleGroup", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询用户所属角色组\n\n @param userId 用户ID\n @return 结果\n"}, {"name": "selectUserPostGroup", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询用户所属岗位组\n\n @param userId 用户ID\n @return 结果\n"}, {"name": "checkUserNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": " 校验用户名称是否唯一\n\n @param user 用户信息\n @return 结果\n"}, {"name": "checkPhoneUnique", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": " 校验手机号码是否唯一\n\n @param user 用户信息\n @return 结果\n"}, {"name": "checkEmailUnique", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": " 校验email是否唯一\n\n @param user 用户信息\n @return 结果\n"}, {"name": "checkUserAllowed", "paramTypes": ["java.lang.Long"], "doc": " 校验用户是否允许操作\n\n @param userId 用户ID\n"}, {"name": "checkUserDataScope", "paramTypes": ["java.lang.Long"], "doc": " 校验用户是否有数据权限\n\n @param userId 用户id\n"}, {"name": "insertUser", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": " 新增用户信息\n\n @param user 用户信息\n @return 结果\n"}, {"name": "registerUser", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "java.lang.String"], "doc": " 注册用户信息\n\n @param user 用户信息\n @return 结果\n"}, {"name": "updateUser", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": " 修改用户信息\n\n @param user 用户信息\n @return 结果\n"}, {"name": "insertUserAuth", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": " 用户授权角色\n\n @param userId  用户ID\n @param roleIds 角色组\n"}, {"name": "updateUserStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 修改用户状态\n\n @param userId 用户ID\n @param status 帐号状态\n @return 结果\n"}, {"name": "updateUserProfile", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": " 修改用户基本信息\n\n @param user 用户信息\n @return 结果\n"}, {"name": "updateUserAvatar", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 修改用户头像\n\n @param userId 用户ID\n @param avatar 头像地址\n @return 结果\n"}, {"name": "resetUserPwd", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 重置用户密码\n\n @param userId   用户ID\n @param password 密码\n @return 结果\n"}, {"name": "deleteUserById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID删除用户\n\n @param userId 用户ID\n @return 结果\n"}, {"name": "deleteUserByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除用户信息\n\n @param userIds 需要删除的用户ID\n @return 结果\n"}, {"name": "selectUserNameById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID查询用户账户\n\n @param userId 用户ID\n @return 用户账户\n"}, {"name": "selectNicknameById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID查询用户账户\n\n @param userId 用户ID\n @return 用户账户\n"}, {"name": "selectNicknameByIds", "paramTypes": ["java.lang.String"], "doc": " 通过用户ID查询用户账户\n\n @param userIds 用户ID\n @return 用户账户\n"}, {"name": "selectPhonenumberById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID查询用户手机号\n\n @param userId 用户id\n @return 用户手机号\n"}, {"name": "selectEmailById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID查询用户邮箱\n\n @param userId 用户id\n @return 用户邮箱\n"}, {"name": "selectUserListByDept", "paramTypes": ["java.lang.Long"], "doc": " 通过部门id查询当前部门所有用户\n\n @param deptId 部门id\n @return 结果\n"}, {"name": "selectUserIdsByRoleIds", "paramTypes": ["java.util.List"], "doc": " 通过角色ID查询用户ID\n\n @param roleIds 角色ids\n @return 用户ids\n"}, {"name": "updateUserPassword", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新用户密码（并升级加密方式）\n\n @param userId      用户ID\n @param newPassword 新的BCrypt密码哈希\n"}, {"name": "selectUserList", "paramTypes": ["java.lang.Object"], "doc": " 根据条件查询用户列表\n\n @param user 用户信息\n @return 用户信息集合信息\n"}], "constructors": []}