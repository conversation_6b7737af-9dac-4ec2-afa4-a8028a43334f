package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__960;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.NekndPerson;
import org.dromara.system.domain.NekndPersonToNekndPersonVoMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__960.class,
    uses = {NekndPersonToNekndPersonVoMapper__14.class},
    imports = {}
)
public interface NekndPersonVoToNekndPersonMapper__14 extends BaseMapper<NekndPersonVo, NekndPerson> {
}
