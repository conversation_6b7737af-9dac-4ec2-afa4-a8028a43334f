{"doc": "\n 企业人才收藏关系（人才收藏岗位，企业收藏人才）Controller\r\n\r\n <AUTHOR>\r\n @date 2025-04-14\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndFavorites", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询企业人才收藏关系列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndFavorites"], "doc": "\n 导出企业人才收藏关系列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取企业人才收藏关系详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": "\n 新增企业人才收藏关系\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": "\n 修改企业人才收藏关系\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除企业人才收藏关系\r\n"}, {"name": "toggleFavorite", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": "\n 收藏和取消收藏\r\n"}, {"name": "getFavorite", "paramTypes": ["org.dromara.business.domain.NekndFavorites", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取当前用户的收藏列表\r\n"}, {"name": "batchToggleFavorite", "paramTypes": ["java.util.List"], "doc": "\n 批量切换收藏状态\r\n"}, {"name": "getFavoriteStatistics", "paramTypes": [], "doc": "\n 获取收藏统计信息\r\n"}, {"name": "checkFavorite", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": "\n 检查是否已收藏\r\n"}], "constructors": []}