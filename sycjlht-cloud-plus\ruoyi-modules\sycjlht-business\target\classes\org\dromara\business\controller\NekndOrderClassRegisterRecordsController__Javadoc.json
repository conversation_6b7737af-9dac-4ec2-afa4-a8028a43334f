{"doc": "\n 订单班报名记录Controller\r\n \r\n <AUTHOR>\r\n @date 2024-08-28\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": "\n 查询订单班报名记录列表\r\n"}, {"name": "orderClassStudents", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据订单班id查看报名该订单班的人员信息\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": "\n 导出订单班报名记录列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取订单班报名记录详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": "\n 新增订单班报名记录\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": "\n 修改订单班报名记录\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除订单班报名记录\r\n"}], "constructors": []}