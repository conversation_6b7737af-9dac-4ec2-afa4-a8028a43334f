{"doc": "\n 师资库Controller\r\n\r\n <AUTHOR>\r\n @date 2024-12-07\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询师资库列表\r\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 匿名查询师资库列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndTeacherPool"], "doc": "\n 导出师资库列表\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取师资库详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool"], "doc": "\n 新增师资库\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool"], "doc": "\n 修改师资库\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除师资库\r\n"}], "constructors": []}