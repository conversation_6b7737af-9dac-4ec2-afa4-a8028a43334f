{"doc": "\n 科普研学报名记录Controller\r\n \r\n <AUTHOR>\r\n @date 2024-08-27\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchRegisterRecords", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询科普研学报名记录列表\r\n"}, {"name": "scientificResearchStudents", "paramTypes": ["java.lang.Integer", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 根据科普研学id查看报名该科普研学的人员信息\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndScientificResearchRegisterRecords"], "doc": "\n 导出科普研学报名记录列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取科普研学报名记录详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchRegisterRecords"], "doc": "\n 新增科普研学报名记录\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchRegisterRecords"], "doc": "\n 修改科普研学报名记录\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除科普研学报名记录\r\n"}, {"name": "getMyRegistrations", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取当前用户的报名记录\r\n"}, {"name": "cancelRegistration", "paramTypes": ["java.lang.Long"], "doc": "\n 取消报名\r\n"}], "constructors": []}