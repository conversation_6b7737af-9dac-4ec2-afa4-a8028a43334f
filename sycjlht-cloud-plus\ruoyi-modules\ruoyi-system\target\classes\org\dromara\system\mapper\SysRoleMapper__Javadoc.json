{"doc": " 角色表 数据层\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectRoleList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 根据条件分页查询角色数据\n\n @param queryWrapper 查询条件\n @return 角色数据集合信息\n"}, {"name": "selectRegisterRoleOptions", "paramTypes": ["java.lang.String"], "doc": " 获取注册可选角色列表（不使用数据权限）\n 返回固定的4个角色：个人、教育端、企业端(需求方)、企业端(服务商)\n\n @param tenantId 租户ID\n @return 角色列表\n"}, {"name": "selectRolePermissionByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询角色\n\n @param userId 用户ID\n @return 角色列表\n"}, {"name": "selectRolesByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询角色\n\n @param userId 用户ID\n @return 角色列表\n"}], "constructors": []}