{"doc": "\n 招聘岗位Controller\r\n \r\n <AUTHOR>\r\n @date 2024-05-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndEmploy", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询招聘岗位列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndEmploy"], "doc": "\n 导出招聘岗位列表\r\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": "\n 导入岗位数据\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取招聘岗位详细信息\r\n"}, {"name": "getPublicInfo", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取招聘岗位详细信息（公开接口）\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": "\n 新增招聘岗位\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": "\n 修改招聘岗位\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除招聘岗位\r\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndEmploy", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询招聘岗位列表（公开接口）\r\n"}, {"name": "getRecommend", "paramTypes": ["org.dromara.business.domain.NekndEmploy", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询推荐招聘岗位列表\r\n"}, {"name": "updateTop", "paramTypes": ["java.lang.Long"], "doc": "\n 岗位置顶\r\n"}, {"name": "cancelTop", "paramTypes": ["java.lang.Long"], "doc": "\n 取消岗位置顶\r\n"}, {"name": "getRefineText", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n AI面试-润化文本\r\n"}, {"name": "getInterviewQuestion", "paramTypes": ["java.lang.String"], "doc": "\n AI面试-出题\r\n"}, {"name": "sendText", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n AI面试-用户发送消息\r\n"}, {"name": "updateStatus", "paramTypes": ["java.lang.String"], "doc": "\n AI面试-结束面试\r\n"}, {"name": "upload", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 上传文件获取fileId\r\n"}, {"name": "postAnalysisResult", "paramTypes": ["org.dromara.business.domain.vo.RequestParamVo"], "doc": "\n 获取分析结果\r\n"}, {"name": "postSendMessage", "paramTypes": ["org.dromara.business.domain.vo.RequestParamVo"], "doc": "\n 对话\r\n"}, {"name": "auditing", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": "\n 修改审核状态\r\n"}, {"name": "getJobDisplay", "paramTypes": [], "doc": "\n 岗位展示列表\r\n"}, {"name": "getRealTimeEmploy", "paramTypes": [], "doc": "\n 发布岗位实时数据\r\n"}, {"name": "getHotJobs", "paramTypes": [], "doc": "\n 岗位投递TOP10\r\n"}, {"name": "getAIJobContent", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n AI智能填充岗位描述或要求\r\n"}, {"name": "getEmployMatch", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": "\n 分析岗位与人才匹配度\r\n"}, {"name": "createNewInterview", "paramTypes": ["org.dromara.business.domain.NekndEmploy", "org.dromara.system.api.model.LoginUser"], "doc": "\n 创建新的面试\r\n"}, {"name": "buildInterviewSystemPrompt", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": "\n 构建面试系统提示\r\n"}, {"name": "saveInterviewRecord", "paramTypes": ["org.dromara.business.domain.NekndEmploy", "org.dromara.system.api.model.LoginUser", "java.util.List"], "doc": "\n 保存面试记录\r\n"}, {"name": "processUserMessage", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord", "java.lang.String"], "doc": "\n 处理用户消息\r\n"}, {"name": "sendInterviewRecordToCompany", "paramTypes": ["org.dromara.business.domain.NekndEmploy", "org.dromara.business.domain.NekndEmployInterviewRecord", "org.dromara.system.api.model.LoginUser", "org.dromara.system.api.domain.vo.RemoteUserVo"], "doc": "\n 发送面试记录到企业\r\n"}, {"name": "callKimiFilesContentApi", "paramTypes": ["java.lang.String"], "doc": "\n 调用Kimi Files Content API\r\n"}, {"name": "processAnalysis", "paramTypes": ["org.dromara.business.domain.vo.RequestParamVo", "java.lang.String"], "doc": "\n 处理分析请求\r\n"}, {"name": "getAnalysisSystemPrompt", "paramTypes": [], "doc": "\n 获取分析系统提示\r\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n HTTP POST调用\r\n"}], "constructors": []}