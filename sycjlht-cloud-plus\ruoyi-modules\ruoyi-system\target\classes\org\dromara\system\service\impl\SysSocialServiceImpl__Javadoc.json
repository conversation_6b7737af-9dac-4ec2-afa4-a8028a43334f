{"doc": "\n 社会化关系Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2023-06-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.String"], "doc": "\n 根据ID查询社会化关系\r\n\r\n @param id 社会化关系的唯一标识符\r\n @return 返回与给定ID对应的SysSocialVo对象，如果未找到则返回null\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": "\n 查询社会化关系列表\r\n\r\n @param bo 用于过滤查询条件的SysSocialBo对象\r\n @return 返回符合条件的SysSocialVo对象列表\r\n"}, {"name": "queryListByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询社会化关系列表\r\n\r\n @param userId 用户的唯一标识符\r\n @return 返回与给定用户ID相关联的SysSocialVo对象列表\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": "\n 新增授权关系\r\n\r\n @param bo 包含新增授权关系信息的SysSocialBo对象\r\n @return 返回新增操作的结果，成功返回true，失败返回false\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": "\n 更新社会化关系\r\n\r\n @param bo 包含更新信息的SysSocialBo对象\r\n @return 返回更新操作的结果，成功返回true，失败返回false\r\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.system.domain.SysSocial"], "doc": "\n 保存前的数据校验\r\n"}, {"name": "deleteWithValidById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除社会化关系信息\r\n\r\n @param id 要删除的社会化关系的唯一标识符\r\n @return 返回删除操作的结果，成功返回true，失败返回false\r\n"}, {"name": "selectByAuthId", "paramTypes": ["java.lang.String"], "doc": "\n 根据认证ID查询社会化关系和用户信息\r\n\r\n @param authId 认证ID\r\n @return 返回包含SysSocial和用户信息的SysSocialVo对象列表\r\n"}], "constructors": []}