{"doc": " 文件上传工具类\n\n <AUTHOR>\n", "fields": [{"name": "DEFAULT_MAX_SIZE", "doc": " 默认大小 50M\n"}, {"name": "DEFAULT_FILE_NAME_LENGTH", "doc": " 默认的文件名最大长度 100\n"}, {"name": "defaultBaseDir", "doc": " 默认上传的地址\n 注意：在微服务架构中，建议使用 RemoteFileService 替代本地文件上传\n"}], "enumConstants": [], "methods": [{"name": "upload", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 以默认配置进行文件上传\n\n @param file 上传的文件\n @return 文件名称\n @throws Exception\n"}, {"name": "upload", "paramTypes": ["java.lang.String", "org.springframework.web.multipart.MultipartFile"], "doc": " 根据文件路径上传\n\n @param baseDir 相对应用的基目录\n @param file    上传的文件\n @return 文件名称\n @throws IOException\n"}, {"name": "upload", "paramTypes": ["java.lang.String", "org.springframework.web.multipart.MultipartFile", "java.lang.String[]"], "doc": " 文件上传\n\n @param baseDir          相对应用的基目录\n @param file             上传的文件\n @param allowedExtension 上传文件类型\n @return 返回上传成功的文件名\n @throws FileSizeLimitExceededException       如果超出最大大小\n @throws FileNameLengthLimitExceededException 文件名太长\n @throws IOException                          比如读写文件出错时\n @throws InvalidExtensionException            文件校验异常\n"}, {"name": "extractFilename", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 编码文件名\n"}, {"name": "assertAllowed", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String[]"], "doc": " 文件大小校验\n\n @param file 上传的文件\n @return\n @throws FileSizeLimitExceededException 如果超出最大大小\n @throws InvalidExtensionException\n"}, {"name": "isAllowedExtension", "paramTypes": ["java.lang.String", "java.lang.String[]"], "doc": " 判断MIME类型是否是允许的MIME类型\n\n @param extension\n @param allowedExtension\n @return\n"}, {"name": "getExtension", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 获取文件名的后缀\n\n @param file 表单文件\n @return 后缀名\n"}, {"name": "uploadByBytes", "paramTypes": ["byte[]", "java.lang.String", "java.lang.String"], "doc": " 根据字节数组上传文件\n\n @param bytes            文件字节数据\n @param baseDir          相对应用的基目录\n @param originalFilename 原始文件名（用于后缀识别）\n @return 文件访问路径\n @throws IOException 如果写入失败\n"}, {"name": "uploadByBytes", "paramTypes": ["byte[]", "java.lang.String", "java.lang.String", "java.lang.String[]"], "doc": " 根据字节数组上传文件，并指定允许的扩展名类型\n\n @param bytes            文件字节数据\n @param baseDir          相对应用的基目录\n @param originalFilename 原始文件名（用于后缀识别）\n @param allowedExtension 允许的扩展名数组\n @return 文件访问路径\n @throws IOException               如果写入失败\n @throws InvalidExtensionException 文件类型不合法\n"}, {"name": "extractFilenameFromOriginal", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据原始文件名生成唯一文件名\n"}, {"name": "getExtension", "paramTypes": ["java.lang.String"], "doc": " 提取文件扩展名\n"}], "constructors": []}