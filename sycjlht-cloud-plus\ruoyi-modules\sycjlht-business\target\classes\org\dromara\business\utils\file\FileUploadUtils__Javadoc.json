{"doc": "\n 文件上传工具类\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "DEFAULT_MAX_SIZE", "doc": "\n 默认大小 50M\r\n"}, {"name": "DEFAULT_FILE_NAME_LENGTH", "doc": "\n 默认的文件名最大长度 100\r\n"}, {"name": "defaultBaseDir", "doc": "\n 默认上传的地址\r\n 注意：在微服务架构中，建议使用 RemoteFileService 替代本地文件上传\r\n"}], "enumConstants": [], "methods": [{"name": "upload", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 以默认配置进行文件上传\r\n\r\n @param file 上传的文件\r\n @return 文件名称\r\n @throws Exception\r\n"}, {"name": "upload", "paramTypes": ["java.lang.String", "org.springframework.web.multipart.MultipartFile"], "doc": "\n 根据文件路径上传\r\n\r\n @param baseDir 相对应用的基目录\r\n @param file    上传的文件\r\n @return 文件名称\r\n @throws IOException\r\n"}, {"name": "upload", "paramTypes": ["java.lang.String", "org.springframework.web.multipart.MultipartFile", "java.lang.String[]"], "doc": "\n 文件上传\r\n\r\n @param baseDir          相对应用的基目录\r\n @param file             上传的文件\r\n @param allowedExtension 上传文件类型\r\n @return 返回上传成功的文件名\r\n @throws FileSizeLimitExceededException       如果超出最大大小\r\n @throws FileNameLengthLimitExceededException 文件名太长\r\n @throws IOException                          比如读写文件出错时\r\n @throws InvalidExtensionException            文件校验异常\r\n"}, {"name": "extractFilename", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 编码文件名\r\n"}, {"name": "assertAllowed", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String[]"], "doc": "\n 文件大小校验\r\n\r\n @param file 上传的文件\r\n @return\r\n @throws FileSizeLimitExceededException 如果超出最大大小\r\n @throws InvalidExtensionException\r\n"}, {"name": "isAllowedExtension", "paramTypes": ["java.lang.String", "java.lang.String[]"], "doc": "\n 判断MIME类型是否是允许的MIME类型\r\n\r\n @param extension\r\n @param allowedExtension\r\n @return\r\n"}, {"name": "getExtension", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 获取文件名的后缀\r\n\r\n @param file 表单文件\r\n @return 后缀名\r\n"}, {"name": "uploadByBytes", "paramTypes": ["byte[]", "java.lang.String", "java.lang.String"], "doc": "\n 根据字节数组上传文件\r\n\r\n @param bytes            文件字节数据\r\n @param baseDir          相对应用的基目录\r\n @param originalFilename 原始文件名（用于后缀识别）\r\n @return 文件访问路径\r\n @throws IOException 如果写入失败\r\n"}, {"name": "uploadByBytes", "paramTypes": ["byte[]", "java.lang.String", "java.lang.String", "java.lang.String[]"], "doc": "\n 根据字节数组上传文件，并指定允许的扩展名类型\r\n\r\n @param bytes            文件字节数据\r\n @param baseDir          相对应用的基目录\r\n @param originalFilename 原始文件名（用于后缀识别）\r\n @param allowedExtension 允许的扩展名数组\r\n @return 文件访问路径\r\n @throws IOException               如果写入失败\r\n @throws InvalidExtensionException 文件类型不合法\r\n"}, {"name": "extractFilenameFromOriginal", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据原始文件名生成唯一文件名\r\n"}, {"name": "getExtension", "paramTypes": ["java.lang.String"], "doc": "\n 提取文件扩展名\r\n"}], "constructors": []}