{"doc": "\n 课程评价记录Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-12-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCoursesEvaluationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询课程评价记录\r\n\r\n @param id 课程评价记录主键\r\n @return 课程评价记录\r\n"}, {"name": "selectNekndCoursesEvaluationList", "paramTypes": ["org.dromara.business.domain.NekndCoursesEvaluation"], "doc": "\n 查询课程评价记录列表\r\n\r\n @param nekndCoursesEvaluation 课程评价记录\r\n @return 课程评价记录集合\r\n"}, {"name": "insertNekndCoursesEvaluation", "paramTypes": ["org.dromara.business.domain.NekndCoursesEvaluation"], "doc": "\n 新增课程评价记录\r\n\r\n @param nekndCoursesEvaluation 课程评价记录\r\n @return 结果\r\n"}, {"name": "updateNekndCoursesEvaluation", "paramTypes": ["org.dromara.business.domain.NekndCoursesEvaluation"], "doc": "\n 修改课程评价记录\r\n\r\n @param nekndCoursesEvaluation 课程评价记录\r\n @return 结果\r\n"}, {"name": "deleteNekndCoursesEvaluationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除课程评价记录\r\n\r\n @param id 课程评价记录主键\r\n @return 结果\r\n"}, {"name": "deleteNekndCoursesEvaluationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除课程评价记录\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}