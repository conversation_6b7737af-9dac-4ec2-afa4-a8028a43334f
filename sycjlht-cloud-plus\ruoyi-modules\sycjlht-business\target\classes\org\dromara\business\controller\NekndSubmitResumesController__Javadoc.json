{"doc": "\n 查看投递简历Controller\r\n \r\n <AUTHOR>\r\n @date 2024-05-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndSubmitResumes", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询查看投递简历列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndSubmitResumes"], "doc": "\n 导出查看投递简历列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取查看投递简历详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndSubmitResumes"], "doc": "\n 新增查看投递简历\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndSubmitResumes"], "doc": "\n 修改查看投递简历\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除查看投递简历\r\n"}, {"name": "getMyResumes", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取当前用户的投递简历列表\r\n"}, {"name": "withdraw", "paramTypes": ["java.lang.Integer"], "doc": "\n 撤回投递的简历\r\n"}], "constructors": []}