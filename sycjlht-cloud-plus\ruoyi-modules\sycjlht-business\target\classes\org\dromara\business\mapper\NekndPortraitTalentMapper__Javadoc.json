{"doc": "\n 数字人才画像Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-11-11\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndPortraitTalentById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询数字人才画像\r\n\r\n @param id 数字人才画像主键\r\n @return 数字人才画像\r\n"}, {"name": "selectPortraitTalentInfoById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询数字人才画像Info\r\n\r\n @param id 数字人才画像主键\r\n @return 数字人才画像\r\n"}, {"name": "selectNekndPortraitTalentList", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent"], "doc": "\n 查询数字人才画像列表\r\n\r\n @param nekndPortraitTalent 数字人才画像\r\n @return 数字人才画像集合\r\n"}, {"name": "selectPortraitTalentList", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent"], "doc": "\n 查询数字人才画像列表\r\n\r\n @param nekndPortraitTalent 数字人才画像\r\n @return 数字人才画像集合\r\n"}, {"name": "insertNekndPortraitTalent", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent"], "doc": "\n 新增数字人才画像\r\n\r\n @param nekndPortraitTalent 数字人才画像\r\n @return 结果\r\n"}, {"name": "updateNekndPortraitTalent", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent"], "doc": "\n 修改数字人才画像\r\n\r\n @param nekndPortraitTalent 数字人才画像\r\n @return 结果\r\n"}, {"name": "deleteNekndPortraitTalentById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除数字人才画像\r\n\r\n @param id 数字人才画像主键\r\n @return 结果\r\n"}, {"name": "deleteNekndPortraitTalentByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除数字人才画像\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}