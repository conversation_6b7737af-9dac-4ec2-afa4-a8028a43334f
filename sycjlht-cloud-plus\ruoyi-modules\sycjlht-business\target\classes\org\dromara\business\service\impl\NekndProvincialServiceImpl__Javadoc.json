{"doc": "\n 省级Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-05-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndProvincialByPid", "paramTypes": ["java.lang.Long"], "doc": "\n 查询省级\r\n\r\n @param pid 省级主键\r\n @return 省级\r\n"}, {"name": "selectNekndProvincialList", "paramTypes": ["org.dromara.business.domain.NekndProvincial"], "doc": "\n 查询省级列表\r\n\r\n @param nekndProvincial 省级\r\n @return 省级\r\n"}, {"name": "insertNekndProvincial", "paramTypes": ["org.dromara.business.domain.NekndProvincial"], "doc": "\n 新增省级\r\n\r\n @param nekndProvincial 省级\r\n @return 结果\r\n"}, {"name": "updateNekndProvincial", "paramTypes": ["org.dromara.business.domain.NekndProvincial"], "doc": "\n 修改省级\r\n\r\n @param nekndProvincial 省级\r\n @return 结果\r\n"}, {"name": "deleteNekndProvincialByPids", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除省级\r\n\r\n @param pids 需要删除的省级主键\r\n @return 结果\r\n"}, {"name": "deleteNekndProvincialByPid", "paramTypes": ["java.lang.Long"], "doc": "\n 删除省级信息\r\n\r\n @param pid 省级主键\r\n @return 结果\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndProvincial", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询省级列表\r\n"}, {"name": "searchProvincials", "paramTypes": ["java.lang.String"], "doc": "\n 搜索省级数据\r\n"}, {"name": "getProvincialStatistics", "paramTypes": [], "doc": "\n 获取省级统计信息\r\n"}], "constructors": []}