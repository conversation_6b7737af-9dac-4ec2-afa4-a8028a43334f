{"doc": "\n 报告权限管理Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-09-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndReportPrivilegeById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询报告权限管理\r\n\r\n @param id 报告权限管理主键\r\n @return 报告权限管理\r\n"}, {"name": "selectNekndReportPrivilegeList", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": "\n 查询报告权限管理列表\r\n\r\n @param nekndReportPrivilege 报告权限管理\r\n @return 报告权限管理\r\n"}, {"name": "insertNekndReportPrivilege", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": "\n 新增报告权限管理\r\n\r\n @param nekndReportPrivilege 报告权限管理\r\n @return 结果\r\n"}, {"name": "updateNekndReportPrivilege", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": "\n 修改报告权限管理\r\n\r\n @param nekndReportPrivilege 报告权限管理\r\n @return 结果\r\n"}, {"name": "deleteNekndReportPrivilegeByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除报告权限管理\r\n\r\n @param ids 需要删除的报告权限管理主键\r\n @return 结果\r\n"}, {"name": "deleteNekndReportPrivilegeById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除报告权限管理信息\r\n\r\n @param id 报告权限管理主键\r\n @return 结果\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询报告权限管理列表\r\n\r\n @param nekndReportPrivilege 报告权限管理\r\n @param pageQuery 分页查询\r\n @return 报告权限管理集合\r\n"}, {"name": "queryUserPrivileges", "paramTypes": ["int", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询用户权限\r\n\r\n @param reportId 报告ID\r\n @param pageQuery 分页查询\r\n @return 用户权限集合\r\n"}, {"name": "checkReportAccess", "paramTypes": ["int", "java.lang.Integer"], "doc": "\n 检查报告访问权限\r\n\r\n @param userId 用户ID\r\n @param reportId 报告ID\r\n @return 是否有访问权限\r\n"}, {"name": "applyReportAccess", "paramTypes": ["int", "java.lang.Integer", "java.lang.String"], "doc": "\n 申请报告访问权限\r\n\r\n @param userId 用户ID\r\n @param reportId 报告ID\r\n @param reason 申请原因\r\n @return 结果\r\n"}, {"name": "auditReportAccess", "paramTypes": ["java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": "\n 审核报告访问权限\r\n\r\n @param privilegeId 权限申请ID\r\n @param status 审核状态\r\n @param auditRemark 审核备注\r\n @return 结果\r\n"}], "constructors": []}