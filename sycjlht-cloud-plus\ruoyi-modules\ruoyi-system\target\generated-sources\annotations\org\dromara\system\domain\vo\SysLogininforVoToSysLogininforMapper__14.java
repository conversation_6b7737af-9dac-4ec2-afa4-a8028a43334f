package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__960;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysLogininfor;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__960.class,
    uses = {SysLogininforToSysLogininforVoMapper__14.class},
    imports = {}
)
public interface SysLogininforVoToSysLogininforMapper__14 extends BaseMapper<SysLogininforVo, SysLogininfor> {
}
