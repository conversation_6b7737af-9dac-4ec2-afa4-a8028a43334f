{"doc": "\n 学生调查信息控制器\r\n \r\n <AUTHOR>\r\n @date 2024-05-21\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询学生调查列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": "\n 导出学生调查列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取学生调查详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": "\n 新增学生调查\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": "\n 修改学生调查\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除学生调查\r\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": "\n 获取学生调查列表（无权限）\r\n"}, {"name": "selectSpeciality", "paramTypes": [], "doc": "\n 按专业筛选\r\n"}, {"name": "selecty", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取学生后台未填写调查\r\n"}, {"name": "exportstu", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": "\n 导出未填写学生调查列表\r\n"}, {"name": "selectN", "paramTypes": [], "doc": "\n 获取学生已填写调查\r\n"}, {"name": "countY", "paramTypes": [], "doc": "\n 统计未填写问卷人数\r\n"}, {"name": "countN", "paramTypes": [], "doc": "\n 统计已填写问卷人数\r\n"}, {"name": "statistics1", "paramTypes": [], "doc": "\n 统计信息1 - 就业意向\r\n"}, {"name": "employmentInfo", "paramTypes": [], "doc": "\n 获取就业信息统计\r\n"}, {"name": "getChartIndustry", "paramTypes": [], "doc": "\n 获取选择工作时，最想进入的行业\r\n"}, {"name": "getChartSalary", "paramTypes": [], "doc": "\n 获取对第一份工作的理想薪资\r\n"}, {"name": "getChartIdea", "paramTypes": [], "doc": "\n 获取择业观念\r\n"}, {"name": "getChartSatisfaction", "paramTypes": [], "doc": "\n 获取对目前工作或就业状态的满意程度\r\n"}], "constructors": []}