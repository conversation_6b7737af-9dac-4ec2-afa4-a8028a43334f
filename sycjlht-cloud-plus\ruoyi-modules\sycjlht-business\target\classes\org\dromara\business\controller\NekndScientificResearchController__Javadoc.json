{"doc": "\n 科普研学信息Controller\r\n \r\n <AUTHOR>\r\n @date 2024-06-27\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询科普研学信息列表\r\n"}, {"name": "adminList", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 管理端查询科普研学信息列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndScientificResearch"], "doc": "\n 导出科普研学信息列表\r\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": "\n 导入科普研学信息\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取科普研学信息详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch"], "doc": "\n 新增科普研学信息\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch"], "doc": "\n 修改科普研学信息\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除科普研学信息\r\n"}, {"name": "auditing", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": "\n 审核科普研学信息\r\n"}, {"name": "filterByGraduateStage", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 按毕业学段筛选科普研学\r\n"}, {"name": "getPopularResearches", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取热门科普研学\r\n"}, {"name": "getMyResearches", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取当前用户创建的科普研学\r\n"}], "constructors": []}