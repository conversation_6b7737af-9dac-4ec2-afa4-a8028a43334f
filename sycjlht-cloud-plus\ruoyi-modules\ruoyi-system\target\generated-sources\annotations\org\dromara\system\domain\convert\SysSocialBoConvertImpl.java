package org.dromara.system.domain.convert;

import javax.annotation.processing.Generated;
import org.dromara.system.api.domain.bo.RemoteSocialBo;
import org.dromara.system.domain.bo.SysSocialBo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:06:26+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Oracle Corporation)"
)
@Component
public class SysSocialBoConvertImpl implements SysSocialBoConvert {

    @Override
    public SysSocialBo convert(RemoteSocialBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysSocialBo sysSocialBo = new SysSocialBo();

        sysSocialBo.setId( arg0.getId() );
        sysSocialBo.setAuthId( arg0.getAuthId() );
        sysSocialBo.setSource( arg0.getSource() );
        sysSocialBo.setAccessToken( arg0.getAccessToken() );
        sysSocialBo.setExpireIn( arg0.getExpireIn() );
        sysSocialBo.setRefreshToken( arg0.getRefreshToken() );
        sysSocialBo.setOpenId( arg0.getOpenId() );
        sysSocialBo.setUserId( arg0.getUserId() );
        sysSocialBo.setAccessCode( arg0.getAccessCode() );
        sysSocialBo.setUnionId( arg0.getUnionId() );
        sysSocialBo.setScope( arg0.getScope() );
        sysSocialBo.setUserName( arg0.getUserName() );
        sysSocialBo.setNickName( arg0.getNickName() );
        sysSocialBo.setEmail( arg0.getEmail() );
        sysSocialBo.setAvatar( arg0.getAvatar() );
        sysSocialBo.setTokenType( arg0.getTokenType() );
        sysSocialBo.setIdToken( arg0.getIdToken() );
        sysSocialBo.setMacAlgorithm( arg0.getMacAlgorithm() );
        sysSocialBo.setMacKey( arg0.getMacKey() );
        sysSocialBo.setCode( arg0.getCode() );
        sysSocialBo.setOauthToken( arg0.getOauthToken() );
        sysSocialBo.setOauthTokenSecret( arg0.getOauthTokenSecret() );

        return sysSocialBo;
    }

    @Override
    public SysSocialBo convert(RemoteSocialBo arg0, SysSocialBo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setAuthId( arg0.getAuthId() );
        arg1.setSource( arg0.getSource() );
        arg1.setAccessToken( arg0.getAccessToken() );
        arg1.setExpireIn( arg0.getExpireIn() );
        arg1.setRefreshToken( arg0.getRefreshToken() );
        arg1.setOpenId( arg0.getOpenId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setAccessCode( arg0.getAccessCode() );
        arg1.setUnionId( arg0.getUnionId() );
        arg1.setScope( arg0.getScope() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setAvatar( arg0.getAvatar() );
        arg1.setTokenType( arg0.getTokenType() );
        arg1.setIdToken( arg0.getIdToken() );
        arg1.setMacAlgorithm( arg0.getMacAlgorithm() );
        arg1.setMacKey( arg0.getMacKey() );
        arg1.setCode( arg0.getCode() );
        arg1.setOauthToken( arg0.getOauthToken() );
        arg1.setOauthTokenSecret( arg0.getOauthTokenSecret() );

        return arg1;
    }
}
