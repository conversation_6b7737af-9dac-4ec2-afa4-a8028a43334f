package org.dromara.business.domain.vo;

import io.github.linpeilie.AutoMapperConfig__1006;
import io.github.linpeilie.BaseMapper;
import org.dromara.business.domain.PolicyNews;
import org.dromara.business.domain.PolicyNewsToPolicyNewsVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1006.class,
    uses = {PolicyNewsToPolicyNewsVoMapper__1.class},
    imports = {}
)
public interface PolicyNewsVoToPolicyNewsMapper__1 extends BaseMapper<PolicyNewsVo, PolicyNews> {
}
