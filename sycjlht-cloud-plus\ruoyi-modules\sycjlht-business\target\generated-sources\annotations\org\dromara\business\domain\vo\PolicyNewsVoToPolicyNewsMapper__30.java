package org.dromara.business.domain.vo;

import io.github.linpeilie.AutoMapperConfig__962;
import io.github.linpeilie.BaseMapper;
import org.dromara.business.domain.PolicyNews;
import org.dromara.business.domain.PolicyNewsToPolicyNewsVoMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__962.class,
    uses = {PolicyNewsToPolicyNewsVoMapper__30.class},
    imports = {}
)
public interface PolicyNewsVoToPolicyNewsMapper__30 extends BaseMapper<PolicyNewsVo, PolicyNews> {
}
