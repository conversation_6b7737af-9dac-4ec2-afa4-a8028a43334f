{"doc": "\n 科研成果Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-05-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndResearchAchievementById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询科研成果\r\n\r\n @param id 科研成果主键\r\n @return 科研成果\r\n"}, {"name": "selectNekndResearchAchievementList", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": "\n 查询科研成果列表\r\n\r\n @param nekndResearchAchievement 科研成果\r\n @return 科研成果\r\n"}, {"name": "insertNekndResearchAchievement", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": "\n 新增科研成果\r\n\r\n @param nekndResearchAchievement 科研成果\r\n @return 结果\r\n"}, {"name": "updateNekndResearchAchievement", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": "\n 修改科研成果\r\n\r\n @param nekndResearchAchievement 科研成果\r\n @return 结果\r\n"}, {"name": "deleteNekndResearchAchievementByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除科研成果\r\n\r\n @param ids 需要删除的科研成果主键\r\n @return 结果\r\n"}, {"name": "deleteNekndResearchAchievementById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除科研成果信息\r\n\r\n @param id 科研成果主键\r\n @return 结果\r\n"}, {"name": "get<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Integer"], "doc": "\n 直接查出三云的科研成果\r\n \r\n @param deptId\r\n @return\r\n"}], "constructors": []}