{"doc": " 科研成果Service业务层处理\n\n <AUTHOR>\n @date 2024-05-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndResearchAchievementById", "paramTypes": ["java.lang.Integer"], "doc": " 查询科研成果\n\n @param id 科研成果主键\n @return 科研成果\n"}, {"name": "selectNekndResearchAchievementList", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": " 查询科研成果列表\n\n @param nekndResearchAchievement 科研成果\n @return 科研成果\n"}, {"name": "insertNekndResearchAchievement", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": " 新增科研成果\n\n @param nekndResearchAchievement 科研成果\n @return 结果\n"}, {"name": "updateNekndResearchAchievement", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": " 修改科研成果\n\n @param nekndResearchAchievement 科研成果\n @return 结果\n"}, {"name": "deleteNekndResearchAchievementByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除科研成果\n\n @param ids 需要删除的科研成果主键\n @return 结果\n"}, {"name": "deleteNekndResearchAchievementById", "paramTypes": ["java.lang.Integer"], "doc": " 删除科研成果信息\n\n @param id 科研成果主键\n @return 结果\n"}, {"name": "get<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Integer"], "doc": " 直接查出三云的科研成果\n \n @param deptId\n @return\n"}], "constructors": []}