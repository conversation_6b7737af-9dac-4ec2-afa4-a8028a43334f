{"doc": "\n 企业信息Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-05-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCompanyById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询企业信息\r\n\r\n @param id 企业信息主键\r\n @return 企业信息\r\n"}, {"name": "selectNekndCompanyList", "paramTypes": ["org.dromara.system.domain.NekndCompany"], "doc": "\n 查询企业信息列表\r\n\r\n @param nekndCompany 企业信息\r\n @return 企业信息集合\r\n"}, {"name": "selectPageNekndCompanyList", "paramTypes": ["org.dromara.system.domain.NekndCompany", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询企业信息列表\r\n\r\n @param nekndCompany 企业信息\r\n @param pageQuery 分页参数\r\n @return 企业信息分页数据\r\n"}, {"name": "insertNekndCompany", "paramTypes": ["org.dromara.system.domain.NekndCompany"], "doc": "\n 新增企业信息\r\n\r\n @param nekndCompany 企业信息\r\n @return 结果\r\n"}, {"name": "updateNekndCompany", "paramTypes": ["org.dromara.system.domain.NekndCompany"], "doc": "\n 修改企业信息\r\n\r\n @param nekndCompany 企业信息\r\n @return 结果\r\n"}, {"name": "deleteNekndCompanyByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除企业信息\r\n\r\n @param ids 需要删除的企业信息主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndCompanyById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除企业信息信息\r\n\r\n @param id 企业信息主键\r\n @return 结果\r\n"}, {"name": "getCompanyLogoByCompanyId", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取企业logo\r\n \r\n @return\r\n"}, {"name": "getCompanyInfoByCompanyId", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据部门id获取企业信息\r\n \r\n @return\r\n"}, {"name": "getCountCompanyNew", "paramTypes": [], "doc": "\n 查询入驻企业数量\r\n \r\n @return\r\n"}, {"name": "deleteNekndCompanyByDeptId", "paramTypes": ["int"], "doc": "\n 根据deptId删除企业信息\r\n \r\n @param deptId\r\n @return\r\n"}, {"name": "getCountEducation", "paramTypes": [], "doc": "\n 查询入驻教育机构数量\r\n \r\n @return\r\n"}, {"name": "getExistingEnterprise", "paramTypes": ["java.lang.String"], "doc": "\n 根据企业名称查询企业是否已存在\r\n true:已存在 false：不存在\r\n"}, {"name": "selectNekndCompanyByDeptId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据部门ID查询企业信息\r\n \r\n @param deptId 部门ID\r\n @return 企业信息\r\n"}], "constructors": []}