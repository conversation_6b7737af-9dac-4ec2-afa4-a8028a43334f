{"doc": "\n 视频观看进度记录Controller\r\n \r\n <AUTHOR>\r\n @date 2024-12-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询视频观看进度记录列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": "\n 导出视频观看进度记录列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取视频观看进度记录详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": "\n 新增视频观看进度记录\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": "\n 修改视频观看进度记录\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除视频观看进度记录\r\n"}, {"name": "getUserProgress", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取用户的视频观看进度\r\n"}, {"name": "updateProgress", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": "\n 更新或创建视频观看进度\r\n"}, {"name": "getStudyStatistics", "paramTypes": [], "doc": "\n 获取用户的学习统计\r\n"}, {"name": "markComplete", "paramTypes": ["java.lang.Integer"], "doc": "\n 标记视频为已完成\r\n"}], "constructors": []}