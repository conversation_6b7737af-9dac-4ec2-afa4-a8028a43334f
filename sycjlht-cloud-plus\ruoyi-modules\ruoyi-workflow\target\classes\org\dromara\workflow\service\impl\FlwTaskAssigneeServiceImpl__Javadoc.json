{"doc": "\n 流程设计器-获取办理人权限设置列表\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getHandlerType", "paramTypes": [], "doc": "\n 获取办理人权限设置列表tabs页签\r\n\r\n @return tabs页签\r\n"}, {"name": "getHandlerSelect", "paramTypes": ["org.dromara.warm.flow.ui.dto.HandlerQuery"], "doc": "\n 获取办理列表, 同时构建左侧部门树状结构\r\n\r\n @param query 查询条件\r\n @return HandlerSelectVo\r\n"}, {"name": "handlerFeedback", "paramTypes": ["java.util.List"], "doc": "\n 办理人权限名称回显\r\n\r\n @param storageIds 入库主键集合\r\n @return 结果\r\n"}, {"name": "fetchTaskAssigneeData", "paramTypes": ["org.dromara.workflow.common.enums.TaskAssigneeEnum", "org.dromara.system.api.domain.bo.RemoteTaskAssigneeBo"], "doc": "\n 根据任务办理类型查询对应的数据\r\n"}, {"name": "fetchDeptData", "paramTypes": ["org.dromara.workflow.common.enums.TaskAssigneeEnum"], "doc": "\n 根据任务办理类型获取部门数据\r\n"}, {"name": "buildDeptTree", "paramTypes": ["java.util.List"], "doc": "\n 构建部门树状结构\r\n"}, {"name": "buildHandlerData", "paramTypes": ["org.dromara.system.api.domain.vo.RemoteTaskAssigneeVo", "org.dromara.workflow.common.enums.TaskAssigneeEnum"], "doc": "\n 构建任务办理人数据\r\n"}, {"name": "fetchUsersByStorageIds", "paramTypes": ["java.lang.String"], "doc": "\n 批量解析多个存储标识符（storageIds），按类型分类并合并查询用户列表\r\n 输入格式支持多个以逗号分隔的标识（如 \"user:123,role:456,789\"）\r\n 会自动去重返回结果，非法格式的标识将被忽略\r\n\r\n @param storageIds 多个存储标识符字符串（逗号分隔）\r\n @return 合并后的用户列表，去重后返回，非法格式的标识将被跳过\r\n"}, {"name": "getUsersByType", "paramTypes": ["org.dromara.workflow.common.enums.TaskAssigneeEnum", "java.util.List"], "doc": "\n 根据指定的任务分配类型（TaskAssigneeEnum）和 ID 列表，获取对应的用户信息列表\r\n\r\n @param type 任务分配类型，表示用户、角色、部门或其他（TaskAssigneeEnum 枚举值）\r\n @param ids  与指定分配类型关联的 ID 列表（例如用户ID、角色ID、部门ID等）\r\n @return 返回包含用户信息的列表。如果类型为用户（USER），则通过用户ID列表查询；\r\n 如果类型为角色（ROLE），则通过角色ID列表查询；\r\n 如果类型为部门（DEPT），则通过部门ID列表查询；\r\n 如果类型为岗位（POST）或无法识别的类型，则返回空列表\r\n"}, {"name": "getNamesByType", "paramTypes": ["org.dromara.workflow.common.enums.TaskAssigneeEnum", "java.util.List"], "doc": "\n 根据任务分配类型和对应 ID 列表，批量查询名称映射关系\r\n\r\n @param type 分配类型（用户、角色、部门、岗位）\r\n @param ids  ID 列表（如用户ID、角色ID等）\r\n @return 返回 Map，其中 key 为 ID，value 为对应的名称\r\n"}, {"name": "parseStorageId", "paramTypes": ["java.lang.String"], "doc": "\n 解析 storageId 字符串，返回类型和ID的组合\r\n\r\n @param storageId 例如 \"user:123\" 或 \"456\"\r\n @return Pair(TaskAssigneeEnum, Long)，如果格式非法返回 null\r\n"}], "constructors": []}