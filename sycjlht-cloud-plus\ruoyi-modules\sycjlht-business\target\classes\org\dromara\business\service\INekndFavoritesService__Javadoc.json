{"doc": "\n 企业人才收藏关系（人才收藏岗位，企业收藏人才）Service接口\r\n\r\n <AUTHOR>\r\n @date 2025-04-14\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndFavoritesById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n\r\n @param id 企业人才收藏关系（人才收藏岗位，企业收藏人才）主键\r\n @return 企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n"}, {"name": "selectNekndFavoritesList", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": "\n 查询企业人才收藏关系（人才收藏岗位，企业收藏人才）列表\r\n\r\n @param nekndFavorites 企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n @return 企业人才收藏关系（人才收藏岗位，企业收藏人才）集合\r\n"}, {"name": "insertNekndFavorites", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": "\n 新增企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n\r\n @param nekndFavorites 企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n @return 结果\r\n"}, {"name": "updateNekndFavorites", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": "\n 修改企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n\r\n @param nekndFavorites 企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n @return 结果\r\n"}, {"name": "deleteNekndFavoritesByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n\r\n @param ids 需要删除的企业人才收藏关系（人才收藏岗位，企业收藏人才）主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndFavoritesById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除企业人才收藏关系（人才收藏岗位，企业收藏人才）信息\r\n\r\n @param id 企业人才收藏关系（人才收藏岗位，企业收藏人才）主键\r\n @return 结果\r\n"}, {"name": "toggleFavorite", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": "\n 新增收藏和修改收藏\r\n @return\r\n"}, {"name": "isFavorite", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": "\n 检查是否收藏\r\n"}, {"name": "getFavoritesCountByUserIdAndType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 获取用户收藏数量（按类型）\r\n\r\n @param userId 用户ID\r\n @param targetType 目标类型：1-岗位，2-人才\r\n @return 收藏数量\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndFavorites", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询收藏列表\r\n\r\n @param nekndFavorites 查询条件\r\n @param pageQuery 分页参数\r\n @return 分页结果\r\n"}], "constructors": []}