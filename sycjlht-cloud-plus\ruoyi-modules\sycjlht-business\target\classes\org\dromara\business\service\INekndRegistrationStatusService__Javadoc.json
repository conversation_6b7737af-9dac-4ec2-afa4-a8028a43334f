{"doc": "\n 培训报名Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-06-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndRegistrationStatusById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询培训报名\r\n\r\n @param id 培训报名主键\r\n @return 培训报名\r\n"}, {"name": "selectNekndRegistrationStatusList", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 查询培训报名列表\r\n\r\n @param nekndRegistrationStatus 培训报名\r\n @return 培训报名集合\r\n"}, {"name": "insertNekndRegistrationStatus", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 新增培训报名\r\n\r\n @param nekndRegistrationStatus 培训报名\r\n @return 结果\r\n"}, {"name": "updateNekndRegistrationStatus", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 修改培训报名\r\n\r\n @param nekndRegistrationStatus 培训报名\r\n @return 结果\r\n"}, {"name": "deleteNekndRegistrationStatusByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除培训报名\r\n\r\n @param ids 需要删除的培训报名主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndRegistrationStatusById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除培训报名信息\r\n\r\n @param id 培训报名主键\r\n @return 结果\r\n"}, {"name": "getPersonalRegistrationCount", "paramTypes": ["java.lang.Long"], "doc": "\n 获取个人报名总数（培训+科普研学+继续教育）\r\n \r\n @param userId 用户ID\r\n @return 报名总数\r\n"}, {"name": "queryEnterpriseTraining", "paramTypes": ["int", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询企业培训\r\n\r\n @param userId 用户ID\r\n @param pageQuery 分页查询\r\n @return 培训列表\r\n"}, {"name": "cancelRegistration", "paramTypes": ["int", "java.lang.Integer"], "doc": "\n 取消报名\r\n\r\n @param userId 用户ID\r\n @param trainingId 培训ID\r\n @return 结果\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询培训报名列表\r\n\r\n @param nekndRegistrationStatus 培训报名\r\n @param pageQuery 分页查询\r\n @return 培训报名集合\r\n"}, {"name": "batch<PERSON><PERSON><PERSON>", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 批量审核\r\n\r\n @param ids 主键集合\r\n @param status 审核状态\r\n @return 结果\r\n"}, {"name": "processRegistration", "paramTypes": ["int", "org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 处理报名\r\n\r\n @param userId 用户ID\r\n @param nekndRegistrationStatus 报名信息\r\n @return 结果\r\n"}, {"name": "getRegistrationStatus", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取报名状态\r\n\r\n @param userId 用户ID\r\n @param trainingId 培训ID\r\n @return 报名状态\r\n"}, {"name": "queryPersonalTraining", "paramTypes": ["int", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询个人培训\r\n\r\n @param userId 用户ID\r\n @param pageQuery 分页查询\r\n @return 培训列表\r\n"}], "constructors": []}