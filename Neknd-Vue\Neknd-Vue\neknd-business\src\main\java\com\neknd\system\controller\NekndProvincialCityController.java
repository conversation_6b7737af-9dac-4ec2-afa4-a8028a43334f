package com.neknd.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.neknd.common.annotation.Anonymous;
import com.neknd.system.domain.NekndProvincial;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.neknd.common.annotation.Log;
import com.neknd.common.core.controller.BaseController;
import com.neknd.common.core.domain.AjaxResult;
import com.neknd.common.enums.BusinessType;
import com.neknd.system.domain.NekndProvincialCity;
import com.neknd.system.service.INekndProvincialCityService;
import com.neknd.common.utils.poi.ExcelUtil;
import com.neknd.common.core.page.TableDataInfo;

/**
 * 市级Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/system/city")
public class NekndProvincialCityController extends BaseController {
    @Autowired
    private INekndProvincialCityService nekndProvincialCityService;

    /**
     * 获取所有省份
     */
    @Anonymous
    @GetMapping(value = "/getList")
    public AjaxResult getList(NekndProvincialCity nekndProvincialCity) {
        List<NekndProvincialCity> list = nekndProvincialCityService.selectNekndProvincialCityList(nekndProvincialCity);
        if (list != null && list.size() > 0 &&nekndProvincialCity!=null&& nekndProvincialCity.getPid()!= null && nekndProvincialCity.getPid() == 18L) {
            //当前是湖北省需要排序，把咸宁市放在第一位
            list.add(0, list.remove(10));
        }
        return success(list);
    }

    /**
     * 获取市级详细信息
     */
    @Anonymous
    @GetMapping(value = "/{cid}")
    public AjaxResult getInfo(@PathVariable("cid") Long cid) {
        return success(nekndProvincialCityService.selectNekndProvincialCityByCid(cid));
    }

}