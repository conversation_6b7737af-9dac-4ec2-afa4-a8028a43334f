{"doc": "\n <AUTHOR>\r\n @date 2024年5月18日\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "listenQueue", "paramTypes": ["org.springframework.amqp.core.Message"], "doc": "\n 普通消息\r\n"}, {"name": "receiveDelayMessage", "paramTypes": ["java.lang.String"], "doc": "\n 处理延迟队列消息\r\n"}, {"name": "receiveDeadMessage", "paramTypes": ["java.lang.String"], "doc": "\n 处理死信队列消息\r\n 当消息在延迟队列中未能被正确处理（例如因消费者逻辑错误、超时未ACK等原因）\r\n 它会被自动转发到死信队列中，以便后续的特殊处理或重新尝试。\r\n"}], "constructors": []}