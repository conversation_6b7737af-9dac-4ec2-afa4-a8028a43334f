package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__960;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__14;
import org.dromara.system.domain.vo.SysOperLogVo;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__960.class,
    uses = {SysOperLogVoToSysOperLogMapper__14.class,SysOperLogBoToSysOperLogMapper__14.class},
    imports = {}
)
public interface SysOperLogToSysOperLogVoMapper__14 extends BaseMapper<SysOperLog, SysOperLogVo> {
}
