{"doc": "\n AI面试记录Controller\r\n \r\n <AUTHOR>\r\n @date 2024-05-29\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询AI面试记录列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndEmployInterviewRecord"], "doc": "\n 导出AI面试记录列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取AI面试记录详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord"], "doc": "\n 新增AI面试记录\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord"], "doc": "\n 修改AI面试记录\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除AI面试记录\r\n"}, {"name": "getStatistics", "paramTypes": [], "doc": "\n 获取用户的面试记录统计信息\r\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.lang.Integer[]", "java.lang.String"], "doc": "\n 批量更新面试记录状态\r\n"}], "constructors": []}