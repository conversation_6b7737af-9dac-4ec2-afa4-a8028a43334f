package io.github.linpeilie;

import org.dromara.business.domain.PolicyNewsToPolicyNewsVoMapper;
import org.dromara.business.domain.bo.PolicyNewsBoToPolicyNewsMapper;
import org.dromara.business.domain.vo.PolicyNewsVoToPolicyNewsMapper;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__962.class, PolicyNewsToPolicyNewsVoMapper.class, PolicyNewsBoToPolicyNewsMapper.class, PolicyNewsVoToPolicyNewsMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__962 {
}
