{"doc": "\n 共同体Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2025-01-02\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCommunityById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询共同体\r\n\r\n @param id 共同体主键\r\n @return 共同体\r\n"}, {"name": "selectNekndCommunityList", "paramTypes": ["org.dromara.business.domain.NekndCommunity"], "doc": "\n 查询共同体列表\r\n\r\n @param nekndCommunity 共同体\r\n @return 共同体\r\n"}, {"name": "insertNekndCommunity", "paramTypes": ["org.dromara.business.domain.NekndCommunity"], "doc": "\n 新增共同体\r\n\r\n @param nekndCommunity 共同体\r\n @return 结果\r\n"}, {"name": "updateNekndCommunity", "paramTypes": ["org.dromara.business.domain.NekndCommunity"], "doc": "\n 修改共同体\r\n\r\n @param nekndCommunity 共同体\r\n @return 结果\r\n"}, {"name": "deleteNekndCommunityByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除共同体\r\n\r\n @param ids 需要删除的共同体主键\r\n @return 结果\r\n"}, {"name": "deleteNekndCommunityById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除共同体信息\r\n\r\n @param id 共同体主键\r\n @return 结果\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndCommunity", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询共同体列表\r\n"}], "constructors": []}