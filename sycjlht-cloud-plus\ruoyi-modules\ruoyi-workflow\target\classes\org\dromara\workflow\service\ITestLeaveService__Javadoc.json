{"doc": "\n 请假Service接口\r\n\r\n <AUTHOR>\r\n @date 2023-07-21\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询请假\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询请假列表\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": "\n 查询请假列表\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": "\n 新增请假\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": "\n 修改请假\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.List"], "doc": "\n 校验并批量删除请假信息\r\n"}], "constructors": []}