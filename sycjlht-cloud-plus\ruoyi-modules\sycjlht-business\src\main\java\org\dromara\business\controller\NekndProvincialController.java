package org.dromara.business.controller;

import java.util.List;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.annotation.SaIgnore;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.business.domain.NekndProvincial;
import org.dromara.business.service.INekndProvincialService;
import org.dromara.system.api.RemoteUserService;

/**
 * 省级管理Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/provincial")
public class NekndProvincialController extends BaseController {

    private final INekndProvincialService nekndProvincialService;
    
    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 获取省级详细信息
     */
    @SaIgnore
    @GetMapping("/{pid}")
    public R<NekndProvincial> getInfo(@NotNull(message = "省份ID不能为空") @PathVariable Long pid) {
        NekndProvincial provincial = nekndProvincialService.selectNekndProvincialByPid(pid);
        return R.ok(provincial);
    }

    /**
     * 获取所有省份列表（湖北省咸宁市优先显示）
     */
    @SaIgnore
    @GetMapping("/getList")
    public R<List<NekndProvincial>> getList() {
        List<NekndProvincial> list = nekndProvincialService.selectNekndProvincialList(new NekndProvincial());
        
        // 特殊处理：将湖北省咸宁市放在第一位（索引17）
        if (list.size() > 17) {
            NekndProvincial xianning = list.remove(17);
            list.add(0, xianning);
        }
        
        return R.ok(list);
    }

    /**
     * 查询省份列表（分页）
     */
    @SaIgnore
    @GetMapping("/list")
    public TableDataInfo<NekndProvincial> list(NekndProvincial nekndProvincial, PageQuery pageQuery) {
        return nekndProvincialService.queryPageList(nekndProvincial, pageQuery);
    }

    /**
     * 搜索省份
     */
    @SaIgnore
    @GetMapping("/search")
    public R<List<NekndProvincial>> search(@RequestParam String keyword) {
        List<NekndProvincial> list = nekndProvincialService.searchProvincials(keyword);
        return R.ok(list);
    }

    /**
     * 获取省份统计信息
     */
    @SaIgnore
    @GetMapping("/statistics")
    public R<Object> getStatistics() {
        return R.ok(nekndProvincialService.getProvincialStatistics());
    }
}
