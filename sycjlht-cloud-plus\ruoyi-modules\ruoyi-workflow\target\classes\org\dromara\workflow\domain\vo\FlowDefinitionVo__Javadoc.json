{"doc": "\n 流程定义视图\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}, {"name": "tenantId", "doc": "\n 租户ID\r\n"}, {"name": "delFlag", "doc": "\n 删除标记\r\n"}, {"name": "flowCode", "doc": "\n 流程定义编码\r\n"}, {"name": "flowName", "doc": "\n 流程定义名称\r\n"}, {"name": "category", "doc": "\n 流程分类id\r\n"}, {"name": "categoryName", "doc": "\n 流程分类名称\r\n"}, {"name": "version", "doc": "\n 流程版本\r\n"}, {"name": "isPublish", "doc": "\n 是否发布（0未发布 1已发布 9失效）\r\n"}, {"name": "formCustom", "doc": "\n 审批表单是否自定义（Y是 N否）\r\n"}, {"name": "formPath", "doc": "\n 审批表单路径\r\n"}, {"name": "activityStatus", "doc": "\n 流程激活状态（0挂起 1激活）\r\n"}, {"name": "listenerType", "doc": "\n 监听器类型\r\n"}, {"name": "listenerPath", "doc": "\n 监听器路径\r\n"}, {"name": "ext", "doc": "\n 扩展字段，预留给业务系统使用\r\n"}], "enumConstants": [], "methods": [], "constructors": []}