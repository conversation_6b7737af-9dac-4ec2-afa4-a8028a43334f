{"doc": " 流程定义视图\n\n <AUTHOR>\n", "fields": [{"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "tenantId", "doc": " 租户ID\n"}, {"name": "delFlag", "doc": " 删除标记\n"}, {"name": "flowCode", "doc": " 流程定义编码\n"}, {"name": "flowName", "doc": " 流程定义名称\n"}, {"name": "category", "doc": " 流程分类id\n"}, {"name": "categoryName", "doc": " 流程分类名称\n"}, {"name": "version", "doc": " 流程版本\n"}, {"name": "isPublish", "doc": " 是否发布（0未发布 1已发布 9失效）\n"}, {"name": "formCustom", "doc": " 审批表单是否自定义（Y是 N否）\n"}, {"name": "formPath", "doc": " 审批表单路径\n"}, {"name": "activityStatus", "doc": " 流程激活状态（0挂起 1激活）\n"}, {"name": "listenerType", "doc": " 监听器类型\n"}, {"name": "listenerPath", "doc": " 监听器路径\n"}, {"name": "ext", "doc": " 扩展字段，预留给业务系统使用\n"}], "enumConstants": [], "methods": [], "constructors": []}