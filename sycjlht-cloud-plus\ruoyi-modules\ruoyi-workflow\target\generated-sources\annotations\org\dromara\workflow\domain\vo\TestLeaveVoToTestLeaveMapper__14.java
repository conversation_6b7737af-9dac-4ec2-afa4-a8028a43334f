package org.dromara.workflow.domain.vo;

import io.github.linpeilie.AutoMapperConfig__961;
import io.github.linpeilie.BaseMapper;
import org.dromara.workflow.domain.TestLeave;
import org.dromara.workflow.domain.TestLeaveToTestLeaveVoMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__961.class,
    uses = {TestLeaveToTestLeaveVoMapper__14.class},
    imports = {}
)
public interface TestLeaveVoToTestLeaveMapper__14 extends BaseMapper<TestLeaveVo, TestLeave> {
}
