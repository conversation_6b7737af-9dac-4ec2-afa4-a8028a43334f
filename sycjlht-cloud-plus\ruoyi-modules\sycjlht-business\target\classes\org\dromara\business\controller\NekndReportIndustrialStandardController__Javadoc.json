{"doc": "\n 工业标准报告Controller\r\n \r\n <AUTHOR>\r\n @date 2024-09-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询工业标准报告列表\r\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 门户查询工业标准报告列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndReportIndustrialStandard"], "doc": "\n 导出工业标准报告列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取工业标准报告详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard"], "doc": "\n 新增工业标准报告\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard"], "doc": "\n 修改工业标准报告\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除工业标准报告\r\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 批量更新报告状态\r\n"}, {"name": "downloadReport", "paramTypes": ["java.lang.Integer"], "doc": "\n 下载报告文件\r\n"}], "constructors": []}