<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.NekndPersonMapper">

    <resultMap type="org.dromara.system.domain.NekndPerson" id="NekndPersonResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="pictureUri" column="picture_uri"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="address" column="address"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="workYear" column="work_year"/>
        <result property="positionStatus" column="position_status"/>
        <result property="technical" column="technical"/>
        <result property="expectedPosition" column="expected_position"/>
        <result property="expectedMoney" column="expected_money"/>
        <result property="jobType" column="job_type"/>
        <result property="workExperienceJson" column="work_experience_json"/>
        <result property="educationalBackgroundJson" column="educational_background_json"/>
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="companyDeptId" column="company_dept_id"/>
        <result property="companyDeptName" column="company_dept_name"/>
        <result property="schoolDeptId" column="school_dept_id"/>
        <result property="schoolDeptName" column="school_dept_name"/>
        <result property="pendingApprovalCompanyDeptId" column="pending_approval_company_dept_id"/>
        <result property="pendingApprovalSchoolDeptId" column="pending_approval_school_dept_id"/>
        <result property="pendingClassify" column="pending_classify"/>
        <result property="acceptanceStatus" column="acceptance_status"/>
        <result property="classify" column="classify"/>
        <result property="educationStatus" column="education_status"/>
        <result property="professionalTitles" column="professional_titles"/>
        <result property="credentialsOfExpertsOrTalents" column="credentials_of_experts_or_talents"/>
        <result property="expertsTypes" column="experts_types"/>
        <result property="status" column="status"/>
        <result property="appointment" column="appointment"/>
        <result property="appointmentTime" column="appointment_time"/>
        <result property="personalExperience" column="personal_experience"/>
        <result property="resume" column="resume"/>
        <result property="resumeApp" column="resume_app"/>
        <result property="certificateHonorUri" column="certificate_honor_uri"/>
        <result property="theme" column="theme"/>
        <result property="isTop" column="is_top"/>
        <result property="updateStatus" column="update_status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createDept" column="create_dept"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectNekndPersonVo">
        select id, user_id, tenant_id, picture_uri, name, sex, address, phone, email, work_year, 
               position_status, technical, expected_position, expected_money, job_type, 
               work_experience_json, educational_background_json, provincial_id, provincial_name, 
               city_id, city_name, company_dept_id, company_dept_name, school_dept_id, school_dept_name,
               pending_approval_company_dept_id, pending_approval_school_dept_id, pending_classify,
               acceptance_status, classify, education_status, professional_titles, 
               credentials_of_experts_or_talents, experts_types, status, appointment, appointment_time,
               personal_experience, resume, resume_app, certificate_honor_uri, theme, is_top, 
               update_status, del_flag, create_dept, create_by, create_time, update_by, update_time, remark
        from neknd_person
    </sql>

    <select id="selectPersonByUserId" parameterType="Long" resultMap="NekndPersonResult">
        <include refid="selectNekndPersonVo"/>
        where user_id = #{userId} and del_flag = '0'
    </select>

    <select id="selectPersonsByUserIds" parameterType="java.util.List" resultMap="NekndPersonResult">
        <include refid="selectNekndPersonVo"/>
        where user_id in
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and del_flag = '0'
    </select>

    <select id="selectPersonsByCompanyDeptId" parameterType="Long" resultMap="NekndPersonResult">
        <include refid="selectNekndPersonVo"/>
        where company_dept_id = #{deptId} and del_flag = '0'
        order by is_top desc, create_time desc
    </select>

    <select id="selectPersonsBySchoolDeptId" parameterType="Long" resultMap="NekndPersonResult">
        <include refid="selectNekndPersonVo"/>
        where school_dept_id = #{deptId} and del_flag = '0'
        order by is_top desc, create_time desc
    </select>

    <update id="updatePersonStatus">
        update neknd_person set status = #{status}, update_time = now()
        where user_id = #{userId} and del_flag = '0'
    </update>

    <update id="updatePersonClassify">
        update neknd_person set classify = #{classify}, update_time = now()
        where user_id = #{userId} and del_flag = '0'
    </update>

    <select id="getClassifyStatistics" resultType="java.util.Map">
        select classify, count(*) as count
        from neknd_person
        where del_flag = '0'
        group by classify
    </select>

    <select id="getEmploymentStatistics" resultType="java.util.Map">
        select position_status, count(*) as count
        from neknd_person
        where del_flag = '0' and position_status is not null
        group by position_status
    </select>

    <select id="selectNekndPersonById" parameterType="Long" resultMap="NekndPersonResult">
        <include refid="selectNekndPersonVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <select id="selectMaxTop" parameterType="Long" resultType="int">
        select IFNULL(MAX(is_top), 0) from neknd_person 
        where del_flag = '0'
        <if test="excludeId != null">
            and id != #{excludeId}
        </if>
    </select>

    <select id="getClassifyStatistics" resultType="map">
        SELECT
            CASE
                WHEN classify = 1 THEN 'classifyYes'
                WHEN classify IN (0, 2) THEN 'classifyNo'
                ELSE '未知'
                END AS classify,
            COUNT(*) as classify_count
        FROM neknd_person
        WHERE classify IS NOT NULL and del_flag = '0' and tenant_id = '075040'
        GROUP BY
            CASE
                WHEN classify = 1 THEN 'classifyYes'
                WHEN classify IN (0, 2) THEN 'classifyNo'
                ELSE '未知'
                END
    </select>

    <select id="getEmploymentStatistics" resultType="map">
        SELECT
            CASE
                WHEN position_status = 1 THEN 'forEmployment'
                ELSE 'unemployed'
                END as positionName,
            COUNT(position_status) as positionCount
        FROM neknd_person
        WHERE position_status != ''
          AND position_status IS NOT NULL
          AND del_flag = '0'
        GROUP BY position_status
    </select>

    <select id="getTalentsAndExpertsCount" resultType="map">
        SELECT
            CASE
                WHEN classify = 1 THEN '人才'
                WHEN classify = 2 THEN '专家'
                ELSE '普通用户'
                END AS classify,
            COUNT(*) as classify_count
        FROM neknd_person
        WHERE (classify = 1 or classify = 2) and classify IS NOT NULL and del_flag = '0'
        GROUP BY classify
    </select>

    <select id="getTalentCategoriesCount" resultType="map">
        SELECT
            CASE
                WHEN education_status = '4' THEN '大专'
                WHEN education_status = '5' THEN '本科'
                WHEN education_status = '6' THEN '硕士'
                WHEN education_status = '7' THEN '博士'
                ELSE '高中及以下'
                END as education_name,
            COUNT(education_status) as education_count
        FROM neknd_person
        WHERE education_status IS NOT NULL and del_flag = '0'
        GROUP BY education_status
    </select>

    <!-- 推荐人员查询SQL - 基于单体系统selectRecommendedList的推荐算法逻辑 -->
    <select id="selectRecommendedList" parameterType="org.dromara.system.domain.bo.NekndPersonBo" resultType="org.dromara.system.domain.vo.NekndPersonVo">
        SELECT 
            id, user_id as userId, tenant_id as tenantId, picture_uri as pictureUri, name, sex, address, 
            phone, email, work_year as workYear, position_status as positionStatus, 
            technical, expected_position as expectedPosition, expected_money as expectedMoney, 
            job_type as jobType, work_experience_json as workExperienceJson, 
            educational_background_json as educationalBackgroundJson, provincial_name as provincialName, 
            city_name as cityName, company_dept_name as companyDeptName, school_dept_name as schoolDeptName,
            pending_approval_company_dept_id as pendingApprovalCompanyDeptId, 
            pending_approval_school_dept_id as pendingApprovalSchoolDeptId, pending_classify as pendingClassify,
            acceptance_status as acceptanceStatus, classify, education_status as educationStatus, 
            professional_titles as professionalTitles, credentials_of_experts_or_talents as credentialsOfExpertsOrTalents,
            experts_types as expertsTypes, status, appointment, appointment_time as appointmentTime, 
            personal_experience as personalExperience, resume, certificate_honor_uri as certificateHonorUri,
            theme, is_top as isTop, update_status as updateStatus, create_time as createTime, 
            update_time as updateTime, remark
        FROM neknd_person
        WHERE del_flag = '0'
          <!-- 推荐算法核心过滤条件，完全基于单体系统的推荐逻辑 -->
          <if test="excludeUserId != null">
              AND user_id &lt;&gt; #{excludeUserId}
          </if>
          AND picture_uri IS NOT NULL AND LENGTH(picture_uri) > 0
          AND name IS NOT NULL AND CHAR_LENGTH(name) > 0
          AND remark IS NOT NULL AND CHAR_LENGTH(remark) > 20
          AND expected_position IS NOT NULL AND CHAR_LENGTH(expected_position) > 0
          AND expected_money IS NOT NULL AND CHAR_LENGTH(expected_money) > 0
          AND work_year IS NOT NULL AND CHAR_LENGTH(work_year) > 0
          AND address IS NOT NULL AND CHAR_LENGTH(address) > 0
          AND CHAR_LENGTH(name) &lt;&gt; LENGTH(name)
          AND educational_background_json IS NOT NULL
          AND JSON_CONTAINS(JSON_EXTRACT(COALESCE(educational_background_json, '{}'), '$[0]'), JSON_OBJECT('schoolName', NULL)) = 0
          AND JSON_CONTAINS(JSON_EXTRACT(COALESCE(educational_background_json, '{}'), '$[0]'), JSON_OBJECT('schoolName', '')) = 0
          <if test="jobType != null and jobType != '' and jobType != '0'">
              AND job_type = #{jobType}
          </if>
          <if test="jobTypeList != null and jobTypeList.size() > 0">
              AND job_type IN
              <foreach item="item" index="index" collection="jobTypeList" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
        ORDER BY is_top DESC, create_time DESC
    </select>

    <!-- ================ AI功能相关SQL ================ -->

    <!-- 更新或保存个人简历信息（AI功能）-->
    <update id="saveOrUpdateNekndPerson">
        UPDATE neknd_person 
        SET resume = #{resume}, update_time = NOW()
        WHERE user_id = #{userId} AND del_flag = '0'
    </update>

    <!-- 更新或保存个人简历应用信息（AI功能）-->
    <update id="saveOrUpdateNekndResumeApp">
        UPDATE neknd_person 
        SET resume_app = #{resumeApp}, update_time = NOW()
        WHERE user_id = #{userId} AND del_flag = '0'
    </update>

    <!-- ================ 预约功能相关SQL ================ -->

    <!-- 更新专家预约信息 -->
    <update id="updateAppointment">
        UPDATE neknd_person 
        SET appointment = #{appointment}, appointment_time = NOW(), update_time = NOW()
        WHERE user_id = #{userId} AND del_flag = '0'
    </update>

    <!-- 获取专家预约信息 -->
    <select id="getAppointment" resultType="java.util.HashMap">
        SELECT appointment_time as appointmentTime, name, appointment
        FROM neknd_person 
        WHERE user_id = #{userId} AND del_flag = '0'
    </select>

    <!-- ================ 主题切换功能SQL ================ -->

    <!-- 更新用户主题设置 -->
    <update id="updateTheme">
        UPDATE neknd_person 
        SET theme = #{theme}, update_time = NOW()
        WHERE id = #{id} AND del_flag = '0'
    </update>

    <!-- ================ 统计展示功能SQL ================ -->

    <!-- 人才展示列表（用于首页展示）-->
    <select id="talentShowcase" resultType="org.dromara.system.domain.vo.NekndPersonVo">
        SELECT id, user_id as userId, tenant_id as tenantId, name, technical, work_year as workYear, 
               picture_uri as pictureUri, address, expected_position as expectedPosition, 
               expected_money as expectedMoney, classify, del_flag as delFlag
        FROM neknd_person 
        WHERE del_flag = '0' AND classify = '1'
        ORDER BY is_top DESC, create_time DESC
        LIMIT 10
    </select>

    <!-- ================ 带动态排序的查询方法 ================ -->
    
    <!-- 分页查询个人信息列表（支持动态排序，基于单体系统的排序逻辑）-->
    <select id="selectPersonListWithDynamicSort" parameterType="org.dromara.system.domain.bo.NekndPersonBo" resultType="org.dromara.system.domain.vo.NekndPersonVo">
        SELECT 
            id, user_id as userId, tenant_id as tenantId, picture_uri as pictureUri, name, sex, address, 
            phone, email, work_year as workYear, position_status as positionStatus, 
            technical, expected_position as expectedPosition, expected_money as expectedMoney, 
            job_type as jobType, work_experience_json as workExperienceJson, 
            educational_background_json as educationalBackgroundJson, provincial_name as provincialName, 
            city_name as cityName, company_dept_name as companyDeptName, school_dept_name as schoolDeptName,
            pending_approval_company_dept_id as pendingApprovalCompanyDeptId, 
            pending_approval_school_dept_id as pendingApprovalSchoolDeptId, pending_classify as pendingClassify,
            acceptance_status as acceptanceStatus, classify, education_status as educationStatus, 
            professional_titles as professionalTitles, credentials_of_experts_or_talents as credentialsOfExpertsOrTalents,
            experts_types as expertsTypes, status, appointment, appointment_time as appointmentTime, 
            personal_experience as personalExperience, resume, certificate_honor_uri as certificateHonorUri,
            theme, is_top as isTop, update_status as updateStatus, create_time as createTime, 
            update_time as updateTime, remark
        FROM neknd_person
        <where>
            del_flag = '0'
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="name != null and name != ''">AND name LIKE CONCAT('%', #{name}, '%')</if>
            <if test="sex != null and sex != ''">AND sex = #{sex}</if>
            <if test="address != null and address != ''">AND address LIKE CONCAT('%', #{address}, '%')</if>
            <if test="phone != null and phone != ''">AND phone LIKE CONCAT('%', #{phone}, '%')</if>
            <if test="email != null and email != ''">AND email = #{email}</if>
            <if test="workYear != null and workYear != ''">AND work_year = #{workYear}</if>
            <if test="positionStatus != null and positionStatus != ''">AND position_status = #{positionStatus}</if>
            <if test="technical != null and technical != ''">AND technical LIKE CONCAT('%', #{technical}, '%')</if>
            <if test="expectedPosition != null and expectedPosition != ''">AND expected_position LIKE CONCAT('%', #{expectedPosition}, '%')</if>
            <if test="expectedMoney != null and expectedMoney != ''">AND expected_money = #{expectedMoney}</if>
            <if test="jobType != null and jobType != '' and jobType != '0'">AND job_type = #{jobType}</if>
            <if test="jobTypeList != null and jobTypeList.size() > 0">
                AND job_type IN
                <foreach item="item" index="index" collection="jobTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companyDeptId != null">AND company_dept_id = #{companyDeptId}</if>
            <if test="schoolDeptId != null">AND school_dept_id = #{schoolDeptId}</if>
            <if test="classify != null and classify != ''">AND classify = #{classify}</if>
            <if test="classifyList != null and classifyList.size() > 0">
                AND classify IN
                <foreach item="item" index="index" collection="classifyList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status != ''">AND status = #{status}</if>
            <if test="educationStatus != null and educationStatus != ''">AND education_status = #{educationStatus}</if>
            <if test="expertsTypes != null and expertsTypes != ''">AND experts_types = #{expertsTypes}</if>
            <if test="theme != null and theme != ''">AND theme = #{theme}</if>
            
            <!-- 数据质量过滤条件（基于单体系统的JSON验证逻辑） -->
            <if test="dataQualityFilter != null and dataQualityFilter == true">
                AND picture_uri IS NOT NULL AND LENGTH(picture_uri) > 0
                AND name IS NOT NULL AND CHAR_LENGTH(name) > 0
                AND remark IS NOT NULL AND CHAR_LENGTH(remark) > 20
                AND expected_position IS NOT NULL AND CHAR_LENGTH(expected_position) > 0
                AND expected_money IS NOT NULL AND CHAR_LENGTH(expected_money) > 0
                AND work_year IS NOT NULL AND CHAR_LENGTH(work_year) > 0
                AND address IS NOT NULL AND CHAR_LENGTH(address) > 0
                AND CHAR_LENGTH(name) &lt;&gt; LENGTH(name)
                AND educational_background_json IS NOT NULL
                AND JSON_CONTAINS(JSON_EXTRACT(COALESCE(educational_background_json, '{}'), '$[0]'), JSON_OBJECT('schoolName', NULL)) = 0
                AND JSON_CONTAINS(JSON_EXTRACT(COALESCE(educational_background_json, '{}'), '$[0]'), JSON_OBJECT('schoolName', '')) = 0
            </if>
        </where>
        
        <!-- 动态构造ORDER BY子句，完全基于单体系统的排序逻辑 -->
        <choose>
            <!-- 当sortMoney和sortWorkYear都有指定时 -->
            <when test="sortMoney != null and sortWorkYear != null">
                ORDER BY
                <choose>
                    <when test="sortMoney == 0">
                        <choose>
                            <when test="sortWorkYear == 0">expected_money+0, work_year+0</when>
                            <otherwise>expected_money+0, work_year+0 DESC</otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        <choose>
                            <when test="sortWorkYear == 0">expected_money+0 DESC, work_year+0</when>
                            <otherwise>expected_money+0 DESC, work_year+0 DESC</otherwise>
                        </choose>
                    </otherwise>
                </choose>
            </when>
            <!-- 当只有sortMoney指定时 -->
            <when test="sortMoney != null and sortWorkYear == null">
                ORDER BY
                <choose>
                    <when test="sortMoney == 0">expected_money+0</when>
                    <otherwise>expected_money+0 DESC</otherwise>
                </choose>
            </when>
            <!-- 当只有sortWorkYear指定时 -->
            <when test="sortMoney == null and sortWorkYear != null">
                ORDER BY
                <choose>
                    <when test="sortWorkYear == 0">work_year+0</when>
                    <otherwise>work_year+0 DESC</otherwise>
                </choose>
            </when>
            <!-- 当两者都未指定时，使用默认排序 -->
            <otherwise>
                ORDER BY is_top DESC, create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询个人信息列表（支持动态排序）-->
    <select id="selectPersonPageListWithDynamicSort" parameterType="org.dromara.system.domain.bo.NekndPersonBo" resultType="org.dromara.system.domain.vo.NekndPersonVo">
        SELECT 
            id, user_id as userId, tenant_id as tenantId, picture_uri as pictureUri, name, sex, address, 
            phone, email, work_year as workYear, position_status as positionStatus, 
            technical, expected_position as expectedPosition, expected_money as expectedMoney, 
            job_type as jobType, work_experience_json as workExperienceJson, 
            educational_background_json as educationalBackgroundJson, provincial_name as provincialName, 
            city_name as cityName, company_dept_name as companyDeptName, school_dept_name as schoolDeptName,
            pending_approval_company_dept_id as pendingApprovalCompanyDeptId, 
            pending_approval_school_dept_id as pendingApprovalSchoolDeptId, pending_classify as pendingClassify,
            acceptance_status as acceptanceStatus, classify, education_status as educationStatus, 
            professional_titles as professionalTitles, credentials_of_experts_or_talents as credentialsOfExpertsOrTalents,
            experts_types as expertsTypes, status, appointment, appointment_time as appointmentTime, 
            personal_experience as personalExperience, resume, certificate_honor_uri as certificateHonorUri,
            theme, is_top as isTop, update_status as updateStatus, create_time as createTime, 
            update_time as updateTime, remark
        FROM neknd_person
        <where>
            del_flag = '0'
            <if test="bo.userId != null">AND user_id = #{bo.userId}</if>
            <if test="bo.name != null and bo.name != ''">AND name LIKE CONCAT('%', #{bo.name}, '%')</if>
            <if test="bo.sex != null and bo.sex != ''">AND sex = #{bo.sex}</if>
            <if test="bo.address != null and bo.address != ''">AND address LIKE CONCAT('%', #{bo.address}, '%')</if>
            <if test="bo.phone != null and bo.phone != ''">AND phone LIKE CONCAT('%', #{bo.phone}, '%')</if>
            <if test="bo.email != null and bo.email != ''">AND email = #{bo.email}</if>
            <if test="bo.workYear != null and bo.workYear != ''">AND work_year = #{bo.workYear}</if>
            <if test="bo.positionStatus != null and bo.positionStatus != ''">AND position_status = #{bo.positionStatus}</if>
            <if test="bo.technical != null and bo.technical != ''">AND technical LIKE CONCAT('%', #{bo.technical}, '%')</if>
            <if test="bo.expectedPosition != null and bo.expectedPosition != ''">AND expected_position LIKE CONCAT('%', #{bo.expectedPosition}, '%')</if>
            <if test="bo.expectedMoney != null and bo.expectedMoney != ''">AND expected_money = #{bo.expectedMoney}</if>
            <if test="bo.jobType != null and bo.jobType != '' and bo.jobType != '0'">AND job_type = #{bo.jobType}</if>
            <if test="bo.jobTypeList != null and bo.jobTypeList.size() > 0">
                AND job_type IN
                <foreach item="item" index="index" collection="bo.jobTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bo.companyDeptId != null">AND company_dept_id = #{bo.companyDeptId}</if>
            <if test="bo.schoolDeptId != null">AND school_dept_id = #{bo.schoolDeptId}</if>
            <if test="bo.classify != null and bo.classify != ''">AND classify = #{bo.classify}</if>
            <if test="bo.classifyList != null and bo.classifyList.size() > 0">
                AND classify IN
                <foreach item="item" index="index" collection="bo.classifyList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bo.status != null and bo.status != ''">AND status = #{bo.status}</if>
            <if test="bo.educationStatus != null and bo.educationStatus != ''">AND education_status = #{bo.educationStatus}</if>
            <if test="bo.expertsTypes != null and bo.expertsTypes != ''">AND experts_types = #{bo.expertsTypes}</if>
            <if test="bo.theme != null and bo.theme != ''">AND theme = #{bo.theme}</if>
            
            <!-- 数据质量过滤条件（基于单体系统的JSON验证逻辑） -->
            <if test="bo.dataQualityFilter != null and bo.dataQualityFilter == true">
                AND picture_uri IS NOT NULL AND LENGTH(picture_uri) > 0
                AND name IS NOT NULL AND CHAR_LENGTH(name) > 0
                AND remark IS NOT NULL AND CHAR_LENGTH(remark) > 20
                AND expected_position IS NOT NULL AND CHAR_LENGTH(expected_position) > 0
                AND expected_money IS NOT NULL AND CHAR_LENGTH(expected_money) > 0
                AND work_year IS NOT NULL AND CHAR_LENGTH(work_year) > 0
                AND address IS NOT NULL AND CHAR_LENGTH(address) > 0
                AND CHAR_LENGTH(name) &lt;&gt; LENGTH(name)
                AND educational_background_json IS NOT NULL
                AND JSON_CONTAINS(JSON_EXTRACT(COALESCE(educational_background_json, '{}'), '$[0]'), JSON_OBJECT('schoolName', NULL)) = 0
                AND JSON_CONTAINS(JSON_EXTRACT(COALESCE(educational_background_json, '{}'), '$[0]'), JSON_OBJECT('schoolName', '')) = 0
            </if>
        </where>
        
        <!-- 动态构造ORDER BY子句，完全基于单体系统的排序逻辑 -->
        <choose>
            <!-- 当sortMoney和sortWorkYear都有指定时 -->
            <when test="bo.sortMoney != null and bo.sortWorkYear != null">
                ORDER BY
                <choose>
                    <when test="bo.sortMoney == 0">
                        <choose>
                            <when test="bo.sortWorkYear == 0">expected_money+0, work_year+0</when>
                            <otherwise>expected_money+0, work_year+0 DESC</otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        <choose>
                            <when test="bo.sortWorkYear == 0">expected_money+0 DESC, work_year+0</when>
                            <otherwise>expected_money+0 DESC, work_year+0 DESC</otherwise>
                        </choose>
                    </otherwise>
                </choose>
            </when>
            <!-- 当只有sortMoney指定时 -->
            <when test="bo.sortMoney != null and bo.sortWorkYear == null">
                ORDER BY
                <choose>
                    <when test="bo.sortMoney == 0">expected_money+0</when>
                    <otherwise>expected_money+0 DESC</otherwise>
                </choose>
            </when>
            <!-- 当只有sortWorkYear指定时 -->
            <when test="bo.sortMoney == null and bo.sortWorkYear != null">
                ORDER BY
                <choose>
                    <when test="bo.sortWorkYear == 0">work_year+0</when>
                    <otherwise>work_year+0 DESC</otherwise>
                </choose>
            </when>
            <!-- 当两者都未指定时，使用默认排序 -->
            <otherwise>
                ORDER BY is_top DESC, create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- ================ 数据质量检查功能 ================ -->

    <!-- 查询不符合数据质量标准的记录（用于管理员数据清理）-->
    <select id="selectDataQualityIssueList" parameterType="org.dromara.system.domain.bo.NekndPersonBo" resultType="org.dromara.system.domain.vo.NekndPersonVo">
        SELECT 
            id, user_id as userId, tenant_id as tenantId, picture_uri as pictureUri, name, sex, address, 
            phone, email, work_year as workYear, position_status as positionStatus, 
            technical, expected_position as expectedPosition, expected_money as expectedMoney, 
            job_type as jobType, work_experience_json as workExperienceJson, 
            educational_background_json as educationalBackgroundJson, provincial_name as provincialName, 
            city_name as cityName, company_dept_name as companyDeptName, school_dept_name as schoolDeptName,
            pending_approval_company_dept_id as pendingApprovalCompanyDeptId, 
            pending_approval_school_dept_id as pendingApprovalSchoolDeptId, pending_classify as pendingClassify,
            acceptance_status as acceptanceStatus, classify, education_status as educationStatus, 
            professional_titles as professionalTitles, credentials_of_experts_or_talents as credentialsOfExpertsOrTalents,
            experts_types as expertsTypes, status, appointment, appointment_time as appointmentTime, 
            personal_experience as personalExperience, resume, certificate_honor_uri as certificateHonorUri,
            theme, is_top as isTop, update_status as updateStatus, create_time as createTime, 
            update_time as updateTime, remark
        FROM neknd_person
        <where>
            del_flag = '0'
            <if test="userId != null">AND user_id = #{userId}</if>
            
            <!-- 数据质量问题过滤条件（与推荐逻辑相反，找出有问题的记录） -->
            AND (
                picture_uri IS NULL OR LENGTH(picture_uri) = 0
                OR name IS NULL OR CHAR_LENGTH(name) = 0
                OR remark IS NULL OR CHAR_LENGTH(remark) &lt;= 20
                OR expected_position IS NULL OR CHAR_LENGTH(expected_position) = 0
                OR expected_money IS NULL OR CHAR_LENGTH(expected_money) = 0
                OR work_year IS NULL OR CHAR_LENGTH(work_year) = 0
                OR address IS NULL OR CHAR_LENGTH(address) = 0
                OR CHAR_LENGTH(name) = LENGTH(name)
                OR educational_background_json IS NULL
                OR JSON_CONTAINS(JSON_EXTRACT(COALESCE(educational_background_json, '{}'), '$[0]'), JSON_OBJECT('schoolName', NULL)) = 1
                OR JSON_CONTAINS(JSON_EXTRACT(COALESCE(educational_background_json, '{}'), '$[0]'), JSON_OBJECT('schoolName', '')) = 1
            )
        </where>
        ORDER BY update_time DESC
    </select>

    <!-- 统计数据质量问题数量 -->
    <select id="countDataQualityIssues" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM neknd_person
        WHERE del_flag = '0'
        AND (
            picture_uri IS NULL OR LENGTH(picture_uri) = 0
            OR name IS NULL OR CHAR_LENGTH(name) = 0
            OR remark IS NULL OR CHAR_LENGTH(remark) &lt;= 20
            OR expected_position IS NULL OR CHAR_LENGTH(expected_position) = 0
            OR expected_money IS NULL OR CHAR_LENGTH(expected_money) = 0
            OR work_year IS NULL OR CHAR_LENGTH(work_year) = 0
            OR address IS NULL OR CHAR_LENGTH(address) = 0
            OR CHAR_LENGTH(name) = LENGTH(name)
            OR educational_background_json IS NULL
            OR JSON_CONTAINS(JSON_EXTRACT(COALESCE(educational_background_json, '{}'), '$[0]'), JSON_OBJECT('schoolName', NULL)) = 1
            OR JSON_CONTAINS(JSON_EXTRACT(COALESCE(educational_background_json, '{}'), '$[0]'), JSON_OBJECT('schoolName', '')) = 1
        )
    </select>

</mapper>
