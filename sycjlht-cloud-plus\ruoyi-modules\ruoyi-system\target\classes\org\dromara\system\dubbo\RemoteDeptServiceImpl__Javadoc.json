{"doc": "\n 部门服务\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectDeptNameByIds", "paramTypes": ["java.lang.String"], "doc": "\n 通过部门ID查询部门名称\r\n\r\n @param deptIds 部门ID串逗号分隔\r\n @return 部门名称串逗号分隔\r\n"}, {"name": "selectDeptLeaderById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据部门ID查询部门负责人\r\n\r\n @param deptId 部门ID，用于指定需要查询的部门\r\n @return 返回该部门的负责人ID\r\n"}, {"name": "selectDeptsByList", "paramTypes": [], "doc": "\n 查询部门\r\n\r\n @return 部门列表\r\n"}], "constructors": []}