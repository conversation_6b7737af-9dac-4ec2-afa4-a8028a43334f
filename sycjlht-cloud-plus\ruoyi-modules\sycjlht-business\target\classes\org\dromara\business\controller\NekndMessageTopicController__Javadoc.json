{"doc": "\n 留言主题Controller\r\n \r\n <AUTHOR>\r\n @date 2024-08-09\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询留言主题列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndMessageTopic"], "doc": "\n 导出留言主题列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取留言主题详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": "\n 新增留言主题\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": "\n 修改留言主题\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除留言主题（逻辑删除）\r\n"}, {"name": "initiateTopic", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": "\n 发起新的留言主题\r\n"}, {"name": "getTopicMessages", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取主题下的消息列表\r\n"}, {"name": "markTopicAsRead", "paramTypes": ["java.lang.Long"], "doc": "\n 标记主题为已读\r\n"}, {"name": "getUnreadTopicCount", "paramTypes": [], "doc": "\n 获取用户的未读主题数量\r\n"}, {"name": "searchTopics", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 搜索主题\r\n"}], "constructors": []}