package org.dromara.workflow.domain;

import io.github.linpeilie.AutoMapperConfig__961;
import io.github.linpeilie.BaseMapper;
import org.dromara.workflow.domain.bo.FlowCategoryBoToFlowCategoryMapper__14;
import org.dromara.workflow.domain.vo.FlowCategoryVo;
import org.dromara.workflow.domain.vo.FlowCategoryVoToFlowCategoryMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__961.class,
    uses = {FlowCategoryVoToFlowCategoryMapper__14.class,FlowCategoryBoToFlowCategoryMapper__14.class},
    imports = {}
)
public interface FlowCategoryToFlowCategoryVoMapper__14 extends BaseMapper<FlowCategory, FlowCategoryVo> {
}
