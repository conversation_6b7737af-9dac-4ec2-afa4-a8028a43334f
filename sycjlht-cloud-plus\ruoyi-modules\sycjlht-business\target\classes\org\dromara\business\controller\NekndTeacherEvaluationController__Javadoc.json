{"doc": "\n 师资评价控制器\r\n \r\n <AUTHOR>\r\n @date 2024-12-09\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndTeacherEvaluation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询师资评价列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndTeacherEvaluation"], "doc": "\n 导出师资评价列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取师资评价详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndTeacherEvaluation"], "doc": "\n 新增师资评价\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndTeacherEvaluation"], "doc": "\n 修改师资评价\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除师资评价\r\n"}], "constructors": []}