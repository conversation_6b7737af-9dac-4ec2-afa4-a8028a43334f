package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__960;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.NekndPersonBoToNekndPersonMapper__14;
import org.dromara.system.domain.vo.NekndPersonVo;
import org.dromara.system.domain.vo.NekndPersonVoToNekndPersonMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__960.class,
    uses = {NekndPersonBoToNekndPersonMapper__14.class,NekndPersonVoToNekndPersonMapper__14.class},
    imports = {}
)
public interface NekndPersonToNekndPersonVoMapper__14 extends BaseMapper<NekndPerson, NekndPersonVo> {
}
