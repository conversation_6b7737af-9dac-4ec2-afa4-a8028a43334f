{"doc": "\n 公益活动Controller\r\n \r\n <AUTHOR>\r\n @date 2024-11-05\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": "\n 查询公益活动列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": "\n 导出公益活动列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取公益活动详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": "\n 新增公益活动\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": "\n 修改公益活动\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除公益活动\r\n"}], "constructors": []}