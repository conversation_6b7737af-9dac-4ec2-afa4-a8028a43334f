{"doc": "\n 科研成果Controller\r\n\r\n <AUTHOR>\r\n @date 2024-05-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询科研成果列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndResearchAchievement"], "doc": "\n 导出科研成果列表\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取科研成果详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": "\n 新增科研成果\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": "\n 修改科研成果\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除科研成果\r\n"}, {"name": "recommendOutcomes", "paramTypes": [], "doc": "\n 推荐成果\r\n"}, {"name": "auditing", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": "\n 审核科研成果\r\n"}, {"name": "getInfoAchievement", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取科研成果详细信息\r\n"}, {"name": "getAchievementDisplay", "paramTypes": [], "doc": "\n 科研成果展示\r\n"}], "constructors": []}