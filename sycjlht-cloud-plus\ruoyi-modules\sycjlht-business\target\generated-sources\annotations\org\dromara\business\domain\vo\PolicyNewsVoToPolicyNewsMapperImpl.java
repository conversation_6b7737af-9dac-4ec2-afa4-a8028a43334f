package org.dromara.business.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.business.domain.PolicyNews;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:06:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Oracle Corporation)"
)
@Component
public class PolicyNewsVoToPolicyNewsMapperImpl implements PolicyNewsVoToPolicyNewsMapper {

    @Override
    public PolicyNews convert(PolicyNewsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PolicyNews policyNews = new PolicyNews();

        policyNews.setCreateTime( arg0.getCreateTime() );
        policyNews.setUpdateTime( arg0.getUpdateTime() );
        policyNews.setId( arg0.getId() );
        policyNews.setCoverUri( arg0.getCoverUri() );
        policyNews.setNewsTitle( arg0.getNewsTitle() );
        policyNews.setNewsType( arg0.getNewsType() );
        policyNews.setNewsContent( arg0.getNewsContent() );
        policyNews.setStatus( arg0.getStatus() );
        policyNews.setSourceTitle( arg0.getSourceTitle() );
        policyNews.setPolicyCategory( arg0.getPolicyCategory() );
        policyNews.setPolicy2category( arg0.getPolicy2category() );
        policyNews.setFileType( arg0.getFileType() );
        policyNews.setIsThematicMeeting( arg0.getIsThematicMeeting() );
        policyNews.setPlanType( arg0.getPlanType() );
        policyNews.setBelongPark( arg0.getBelongPark() );
        policyNews.setNewsPosition( arg0.getNewsPosition() );
        policyNews.setFileTypeFunds( arg0.getFileTypeFunds() );
        policyNews.setBelongDistrict( arg0.getBelongDistrict() );
        policyNews.setPreviousId( arg0.getPreviousId() );
        policyNews.setPreviousTitle( arg0.getPreviousTitle() );
        policyNews.setNextId( arg0.getNextId() );
        policyNews.setNextTitle( arg0.getNextTitle() );

        return policyNews;
    }

    @Override
    public PolicyNews convert(PolicyNewsVo arg0, PolicyNews arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setId( arg0.getId() );
        arg1.setCoverUri( arg0.getCoverUri() );
        arg1.setNewsTitle( arg0.getNewsTitle() );
        arg1.setNewsType( arg0.getNewsType() );
        arg1.setNewsContent( arg0.getNewsContent() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setSourceTitle( arg0.getSourceTitle() );
        arg1.setPolicyCategory( arg0.getPolicyCategory() );
        arg1.setPolicy2category( arg0.getPolicy2category() );
        arg1.setFileType( arg0.getFileType() );
        arg1.setIsThematicMeeting( arg0.getIsThematicMeeting() );
        arg1.setPlanType( arg0.getPlanType() );
        arg1.setBelongPark( arg0.getBelongPark() );
        arg1.setNewsPosition( arg0.getNewsPosition() );
        arg1.setFileTypeFunds( arg0.getFileTypeFunds() );
        arg1.setBelongDistrict( arg0.getBelongDistrict() );
        arg1.setPreviousId( arg0.getPreviousId() );
        arg1.setPreviousTitle( arg0.getPreviousTitle() );
        arg1.setNextId( arg0.getNextId() );
        arg1.setNextTitle( arg0.getNextTitle() );

        return arg1;
    }
}
