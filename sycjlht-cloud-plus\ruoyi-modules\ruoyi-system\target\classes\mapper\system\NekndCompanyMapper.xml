<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.NekndCompanyMapper">

    <resultMap type="org.dromara.system.domain.NekndCompany" id="NekndCompanyResult">
        <result property="id"    column="id"    />
        <result property="companyLogoUri"    column="company_logo_uri"    />
        <result property="companyName"    column="company_name"    />
        <result property="companyShortName"    column="company_short_name"    />
        <result property="address"    column="address"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="industry"    column="industry"    />
        <result property="industryLevelOne"    column="industry_level_one"    />
        <result property="industryLevelTwo"    column="industry_level_two"    />
        <result property="companyType"    column="company_type"    />
        <result property="belongingPark"    column="belonging_park"    />
        <result property="summary"    column="summary"    />
        <result property="businessLicenseUri"    column="business_license_uri"    />
        <result property="businessLicenseNo"    column="business_license_no"    />
        <result property="introducePictureUri"    column="introduce_picture_uri"    />
        <result property="introduceVideoUri"    column="introduce_video_uri"    />
        <result property="schoolCode"    column="school_code"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="deptId"    column="dept_id"    />
        <result property="status"    column="status"    />
        <result property="updateStatus"    column="update_status"    />
        <result property="tenantId"    column="tenant_id"    />
    </resultMap>

    <!-- 获取企业Logo -->
    <select id="getCompanyLogoByCompanyId" parameterType="java.lang.Integer" resultType="java.lang.String">
        select company_logo_uri from neknd_company 
        where dept_id = #{deptId} and del_flag = '0'
    </select>

    <!-- 根据部门ID获取企业名称 -->
    <select id="selectName" parameterType="java.lang.Integer" resultMap="NekndCompanyResult">
        select company_name from neknd_company 
        where dept_id = #{deptId} and del_flag = '0'
    </select>

    <!-- 根据部门ID获取企业信息 -->
    <select id="getCompanyInfoByCompanyId" parameterType="java.lang.Integer" resultMap="NekndCompanyResult">
        select * from neknd_company 
        where dept_id = #{deptId} and del_flag = '0'
    </select>

    <!-- 根据用户ID获取企业信息 -->
    <select id="selectCompanyInfoByUserId" parameterType="java.lang.Integer" resultMap="NekndCompanyResult">
        select c.* from neknd_company c
        left join sys_user u on c.dept_id = u.dept_id
        where u.user_id = #{userId} and c.del_flag = '0'
    </select>

    <!-- 企业类别分布统计 -->
    <resultMap id="businessCategoriesMap" type="java.util.HashMap">
        <result property="name" column="industry_name"/>
        <result property="count" column="industry_count"/>
    </resultMap>
    <select id="getBusinessCategories" resultMap="businessCategoriesMap">
        SELECT
            CASE
                WHEN industry = 1 THEN '信息技术'
                WHEN industry = 2 THEN '金融'
                WHEN industry = 3 THEN '地产建筑'
                WHEN industry = 4 THEN '医药'
                WHEN industry = 5 THEN '消费品'
                WHEN industry = 6 THEN '教育'
                WHEN industry = 7 THEN '文化传媒'
                WHEN industry = 8 THEN '制造业'
                ELSE '其他'
                END AS industry_name,
            COUNT(industry) as industry_count
        FROM neknd_company
        WHERE industry !="" and industry is not NULL and del_flag = '0' and status = '0'
        GROUP BY industry_name
    </select>

    <!-- 企业类型分布统计 -->
    <resultMap id="companyTypeMap" type="java.util.HashMap">
        <result property="companyType" column="company_type"/>
        <result property="count" column="company_type_count"/>
    </resultMap>
    <select id="getCompanyType" parameterType="java.lang.String" resultMap="companyTypeMap">
        select company_type AS company_type, count(company_type) AS company_type_count 
        from neknd_company
        where del_flag = '0' and company_type is not null
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        GROUP BY company_type
    </select>

    <!-- 检查企业名称是否已存在 -->
    <select id="getCountExistingEnterprise" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT count(1) FROM neknd_company 
        where del_flag = '0' and update_status = 1 and company_name = #{companyName}
    </select>

    <!-- 获取服务商企业数量（关联用户角色表） -->
    <select id="getCompanyService" resultType="java.lang.Integer">
        select count(1) from neknd_company c 
        left join sys_user u on c.dept_id = u.dept_id 
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on ur.role_id = r.role_id
        where c.del_flag = '0' and r.role_key = 'enterpriseServicer'
    </select>

</mapper>