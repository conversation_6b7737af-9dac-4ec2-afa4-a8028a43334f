{"doc": " S3 存储协议 所有兼容S3协议的云厂商均支持\n 阿里云 腾讯云 七牛云 minio\n\n <AUTHOR>\n", "fields": [{"name": "config<PERSON><PERSON>", "doc": " 服务商\n"}, {"name": "properties", "doc": " 配置属性\n"}, {"name": "client", "doc": " Amazon S3 异步客户端\n"}, {"name": "transferManager", "doc": " 用于管理 S3 数据传输的高级工具\n"}, {"name": "presigner", "doc": " AWS S3 预签名 URL 的生成器\n"}], "enumConstants": [], "methods": [{"name": "upload", "paramTypes": ["java.nio.file.Path", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 上传文件到 Amazon S3，并返回上传结果\n\n @param filePath    本地文件路径\n @param key         在 Amazon S3 中的对象键\n @param md5Digest   本地文件的 MD5 哈希值（可选）\n @param contentType 文件内容类型\n @return UploadResult 包含上传后的文件信息\n @throws OssException 如果上传失败，抛出自定义异常\n"}, {"name": "upload", "paramTypes": ["java.io.InputStream", "java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 上传 InputStream 到 Amazon S3\n\n @param inputStream 要上传的输入流\n @param key         在 Amazon S3 中的对象键\n @param length      输入流的长度\n @param contentType 文件内容类型\n @return UploadResult 包含上传后的文件信息\n @throws OssException 如果上传失败，抛出自定义异常\n"}, {"name": "fileDownload", "paramTypes": ["java.lang.String"], "doc": " 下载文件从 Amazon S3 到临时目录\n\n @param path 文件在 Amazon S3 中的对象键\n @return 下载后的文件在本地的临时路径\n @throws OssException 如果下载失败，抛出自定义异常\n"}, {"name": "download", "paramTypes": ["java.lang.String", "java.io.OutputStream", "java.util.function.Consumer"], "doc": " 下载文件从 Amazon S3 到 输出流\n\n @param key 文件在 Amazon S3 中的对象键\n @param out 输出流\n @return 输出流中写入的字节数（长度）\n @throws OssException 如果下载失败，抛出自定义异常\n"}, {"name": "delete", "paramTypes": ["java.lang.String"], "doc": " 删除云存储服务中指定路径下文件\n\n @param path 指定路径\n"}, {"name": "getPrivateUrl", "paramTypes": ["java.lang.String", "java.time.Duration"], "doc": " 获取私有URL链接\n\n @param objectKey   对象KEY\n @param expiredTime 链接授权到期时间\n"}, {"name": "uploadSuffix", "paramTypes": ["byte[]", "java.lang.String", "java.lang.String"], "doc": " 上传 byte[] 数据到 Amazon S3，使用指定的后缀构造对象键。\n\n @param data   要上传的 byte[] 数据\n @param suffix 对象键的后缀\n @return UploadResult 包含上传后的文件信息\n @throws OssException 如果上传失败，抛出自定义异常\n"}, {"name": "uploadSuffix", "paramTypes": ["java.io.InputStream", "java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 上传 InputStream 到 Amazon S3，使用指定的后缀构造对象键。\n\n @param inputStream 要上传的输入流\n @param suffix      对象键的后缀\n @param length      输入流的长度\n @return UploadResult 包含上传后的文件信息\n @throws OssException 如果上传失败，抛出自定义异常\n"}, {"name": "uploadSuffix", "paramTypes": ["java.io.File", "java.lang.String"], "doc": " 上传文件到 Amazon S3，使用指定的后缀构造对象键\n\n @param file   要上传的文件\n @param suffix 对象键的后缀\n @return UploadResult 包含上传后的文件信息\n @throws OssException 如果上传失败，抛出自定义异常\n"}, {"name": "getObjectContent", "paramTypes": ["java.lang.String"], "doc": " 获取文件输入流\n\n @param path 完整文件路径\n @return 输入流\n"}, {"name": "getEndpoint", "paramTypes": [], "doc": " 获取 S3 客户端的终端点 URL\n\n @return 终端点 URL\n"}, {"name": "getDomain", "paramTypes": [], "doc": " 获取 S3 客户端的终端点 URL（自定义域名）\n\n @return 终端点 URL\n"}, {"name": "of", "paramTypes": [], "doc": " 根据传入的 region 参数返回相应的 AWS 区域\n 如果 region 参数非空，使用 Region.of 方法创建并返回对应的 AWS 区域对象\n 如果 region 参数为空，返回一个默认的 AWS 区域（例如，us-east-1），作为广泛支持的区域\n\n @return 对应的 AWS 区域对象，或者默认的广泛支持的区域（us-east-1）\n"}, {"name": "getUrl", "paramTypes": [], "doc": " 获取云存储服务的URL\n\n @return 文件路径\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 生成一个符合特定规则的、唯一的文件路径。通过使用日期、UUID、前缀和后缀等元素的组合，确保了文件路径的独一无二性\n\n @param prefix 前缀\n @param suffix 后缀\n @return 文件路径\n"}, {"name": "removeBaseUrl", "paramTypes": ["java.lang.String"], "doc": " 移除路径中的基础URL部分，得到相对路径\n\n @param path 完整的路径，包括基础URL和相对路径\n @return 去除基础URL后的相对路径\n"}, {"name": "getConfigKey", "paramTypes": [], "doc": " 服务商\n"}, {"name": "getIsHttps", "paramTypes": [], "doc": " 获取是否使用 HTTPS 的配置，并返回相应的协议头部。\n\n @return 协议头部，根据是否使用 HTTPS 返回 \"https://\" 或 \"http://\"\n"}, {"name": "checkPropertiesSame", "paramTypes": ["org.dromara.common.oss.properties.OssProperties"], "doc": " 检查配置是否相同\n"}, {"name": "getAccessPolicy", "paramTypes": [], "doc": " 获取当前桶权限类型\n\n @return 当前桶权限类型code\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.lang.String", "org.dromara.common.oss.properties.OssProperties"], "doc": " 构造方法\n\n @param configKey     配置键\n @param ossProperties Oss配置属性\n"}]}