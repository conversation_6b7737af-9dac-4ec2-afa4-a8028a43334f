package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__960;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictType;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__960.class,
    uses = {SysDictTypeToSysDictTypeVoMapper__14.class},
    imports = {}
)
public interface SysDictTypeVoToSysDictTypeMapper__14 extends BaseMapper<SysDictTypeVo, SysDictType> {
}
