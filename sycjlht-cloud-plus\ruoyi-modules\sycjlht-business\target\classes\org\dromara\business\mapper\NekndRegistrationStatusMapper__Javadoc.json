{"doc": "\n 培训报名Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-06-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndRegistrationStatusById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询培训报名\r\n\r\n @param id 培训报名主键\r\n @return 培训报名\r\n"}, {"name": "selectNekndRegistrationStatusList", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 查询培训报名列表\r\n\r\n @param nekndRegistrationStatus 培训报名\r\n @return 培训报名集合\r\n"}, {"name": "insertNekndRegistrationStatus", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 新增培训报名\r\n\r\n @param nekndRegistrationStatus 培训报名\r\n @return 结果\r\n"}, {"name": "updateNekndRegistrationStatus", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 修改培训报名\r\n\r\n @param nekndRegistrationStatus 培训报名\r\n @return 结果\r\n"}, {"name": "deleteNekndRegistrationStatusById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除培训报名\r\n\r\n @param id 培训报名主键\r\n @return 结果\r\n"}, {"name": "deleteNekndRegistrationStatusByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除培训报名\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}, {"name": "getPersonalRegistrationCount", "paramTypes": ["java.lang.Long"], "doc": "\n 获取个人报名总数（培训+科普研学+继续教育）\r\n \r\n @param userId 用户ID\r\n @return 报名总数\r\n"}], "constructors": []}