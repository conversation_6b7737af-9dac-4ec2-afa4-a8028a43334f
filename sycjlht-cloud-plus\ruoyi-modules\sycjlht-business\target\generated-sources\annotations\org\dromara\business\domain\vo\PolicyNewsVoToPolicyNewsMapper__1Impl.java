package org.dromara.business.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.business.domain.PolicyNews;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-14T20:25:51+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class PolicyNewsVoToPolicyNewsMapper__1Impl implements PolicyNewsVoToPolicyNewsMapper__1 {

    @Override
    public PolicyNews convert(PolicyNewsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PolicyNews policyNews = new PolicyNews();

        policyNews.setCreateTime( arg0.getCreateTime() );
        policyNews.setUpdateTime( arg0.getUpdateTime() );
        policyNews.setBelongDistrict( arg0.getBelongDistrict() );
        policyNews.setBelongPark( arg0.getBelongPark() );
        policyNews.setCoverUri( arg0.getCoverUri() );
        policyNews.setFileType( arg0.getFileType() );
        policyNews.setFileTypeFunds( arg0.getFileTypeFunds() );
        policyNews.setId( arg0.getId() );
        policyNews.setIsThematicMeeting( arg0.getIsThematicMeeting() );
        policyNews.setNewsContent( arg0.getNewsContent() );
        policyNews.setNewsPosition( arg0.getNewsPosition() );
        policyNews.setNewsTitle( arg0.getNewsTitle() );
        policyNews.setNewsType( arg0.getNewsType() );
        policyNews.setNextId( arg0.getNextId() );
        policyNews.setNextTitle( arg0.getNextTitle() );
        policyNews.setPlanType( arg0.getPlanType() );
        policyNews.setPolicy2category( arg0.getPolicy2category() );
        policyNews.setPolicyCategory( arg0.getPolicyCategory() );
        policyNews.setPreviousId( arg0.getPreviousId() );
        policyNews.setPreviousTitle( arg0.getPreviousTitle() );
        policyNews.setSourceTitle( arg0.getSourceTitle() );
        policyNews.setStatus( arg0.getStatus() );

        return policyNews;
    }

    @Override
    public PolicyNews convert(PolicyNewsVo arg0, PolicyNews arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setBelongDistrict( arg0.getBelongDistrict() );
        arg1.setBelongPark( arg0.getBelongPark() );
        arg1.setCoverUri( arg0.getCoverUri() );
        arg1.setFileType( arg0.getFileType() );
        arg1.setFileTypeFunds( arg0.getFileTypeFunds() );
        arg1.setId( arg0.getId() );
        arg1.setIsThematicMeeting( arg0.getIsThematicMeeting() );
        arg1.setNewsContent( arg0.getNewsContent() );
        arg1.setNewsPosition( arg0.getNewsPosition() );
        arg1.setNewsTitle( arg0.getNewsTitle() );
        arg1.setNewsType( arg0.getNewsType() );
        arg1.setNextId( arg0.getNextId() );
        arg1.setNextTitle( arg0.getNextTitle() );
        arg1.setPlanType( arg0.getPlanType() );
        arg1.setPolicy2category( arg0.getPolicy2category() );
        arg1.setPolicyCategory( arg0.getPolicyCategory() );
        arg1.setPreviousId( arg0.getPreviousId() );
        arg1.setPreviousTitle( arg0.getPreviousTitle() );
        arg1.setSourceTitle( arg0.getSourceTitle() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
