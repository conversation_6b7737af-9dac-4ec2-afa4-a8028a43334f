{"doc": "\n 社会化关系服务\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByAuthId", "paramTypes": ["java.lang.String"], "doc": "\n 根据 authId 查询用户授权信息\r\n\r\n @param authId 认证id\r\n @return 授权信息\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.api.domain.bo.RemoteSocialBo"], "doc": "\n 查询列表\r\n\r\n @param bo 社会化关系业务对象\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.api.domain.bo.RemoteSocialBo"], "doc": "\n 保存社会化关系\r\n\r\n @param bo 社会化关系业务对象\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.api.domain.bo.RemoteSocialBo"], "doc": "\n 更新社会化关系\r\n\r\n @param bo 社会化关系业务对象\r\n"}, {"name": "deleteWithValidById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除社会化关系\r\n\r\n @param socialId 社会化关系ID\r\n @return 结果\r\n"}], "constructors": []}