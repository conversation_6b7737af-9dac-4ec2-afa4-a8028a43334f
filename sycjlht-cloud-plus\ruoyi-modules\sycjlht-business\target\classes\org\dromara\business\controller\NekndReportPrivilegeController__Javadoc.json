{"doc": "\n 报告权限管理Controller\r\n \r\n <AUTHOR>\r\n @date 2024-09-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询报告权限管理列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndReportPrivilege"], "doc": "\n 导出报告权限管理列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取报告权限管理详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": "\n 申请查看和下载报告权限\r\n"}, {"name": "audit", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": "\n 审核报告权限申请\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": "\n 修改报告权限管理\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除报告权限管理\r\n"}, {"name": "checkPermission", "paramTypes": [], "doc": "\n 检查当前用户是否有报告查看权限\r\n"}, {"name": "getMyStatus", "paramTypes": [], "doc": "\n 获取当前用户的权限申请状态\r\n"}], "constructors": []}