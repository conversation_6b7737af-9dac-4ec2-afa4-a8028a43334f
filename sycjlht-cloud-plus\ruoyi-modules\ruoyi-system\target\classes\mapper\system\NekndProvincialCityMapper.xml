<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.NekndProvincialCityMapper">
    
    <resultMap type="NekndProvincialCity" id="NekndProvincialCityResult">
        <result property="cid"    column="cid"    />
        <result property="cname"    column="cname"    />
        <result property="pid"    column="pid"    />
    </resultMap>

    <sql id="selectNekndProvincialCityVo">
        select cid, cname, pid from neknd_provincial_city
    </sql>

    <select id="selectNekndProvincialCityList" parameterType="NekndProvincialCity" resultMap="NekndProvincialCityResult">
        <include refid="selectNekndProvincialCityVo"/>
        <where>  
            <if test="cname != null  and cname != ''"> and cname like concat('%', #{cname}, '%')</if>
            <if test="pid != null "> and pid = #{pid}</if>
        </where>
    </select>
    
    <select id="selectNekndProvincialCityByCid" parameterType="Long" resultMap="NekndProvincialCityResult">
        <include refid="selectNekndProvincialCityVo"/>
        where cid = #{cid}
    </select>
        
    <insert id="insertNekndProvincialCity" parameterType="NekndProvincialCity" useGeneratedKeys="true" keyProperty="cid">
        insert into neknd_provincial_city
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cname != null and cname != ''">cname,</if>
            <if test="pid != null">pid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cname != null and cname != ''">#{cname},</if>
            <if test="pid != null">#{pid},</if>
         </trim>
    </insert>

    <update id="updateNekndProvincialCity" parameterType="NekndProvincialCity">
        update neknd_provincial_city
        <trim prefix="SET" suffixOverrides=",">
            <if test="cname != null and cname != ''">cname = #{cname},</if>
            <if test="pid != null">pid = #{pid},</if>
        </trim>
        where cid = #{cid}
    </update>

    <delete id="deleteNekndProvincialCityByCid" parameterType="Long">
        delete from neknd_provincial_city where cid = #{cid}
    </delete>

    <delete id="deleteNekndProvincialCityByCids" parameterType="String">
        delete from neknd_provincial_city where cid in 
        <foreach item="cid" collection="array" open="(" separator="," close=")">
            #{cid}
        </foreach>
    </delete>

    <select id="selectNekndProvincialCityIdByName" resultType="Long">
        SELECT cid
        FROM neknd_provincial_city
        WHERE cname= #{cname}
    </select>

    <resultMap id="cityNames" type="hashmap">
        <result property="cid" column="cid" />
        <result property="cname" column="cname" />
        <result property="pid" column="pid" />
    </resultMap>
    <select id="getCityNames" parameterType="java.util.Set" resultMap="cityNames">
        SELECT
            cid, cname, pid
        FROM
            neknd_provincial_city
        where
            cid in
        <foreach item="id" collection="keys" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
