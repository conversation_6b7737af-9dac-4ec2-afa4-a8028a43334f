{"doc": "\n 全局任务办理监听\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "create", "paramTypes": ["org.dromara.warm.flow.core.listener.ListenerVariable"], "doc": "\n 创建监听器，任务创建时执行\r\n\r\n @param listenerVariable 监听器变量\r\n"}, {"name": "start", "paramTypes": ["org.dromara.warm.flow.core.listener.ListenerVariable"], "doc": "\n 开始监听器，任务开始办理时执行\r\n\r\n @param listenerVariable 监听器变量\r\n"}, {"name": "assignment", "paramTypes": ["org.dromara.warm.flow.core.listener.ListenerVariable"], "doc": "\n 分派监听器，动态修改代办任务信息\r\n\r\n @param listenerVariable 监听器变量\r\n"}, {"name": "finish", "paramTypes": ["org.dromara.warm.flow.core.listener.ListenerVariable"], "doc": "\n 完成监听器，当前任务完成后执行\r\n\r\n @param listenerVariable 监听器变量\r\n"}, {"name": "determineFlowStatus", "paramTypes": ["org.dromara.warm.flow.core.entity.Instance"], "doc": "\n 根据流程实例确定最终状态\r\n\r\n @param instance 流程实例\r\n @return 流程最终状态\r\n"}], "constructors": []}