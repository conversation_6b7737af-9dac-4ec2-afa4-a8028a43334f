{"doc": "\n 参数配置 服务层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectConfigById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询参数配置信息\r\n\r\n @param configId 参数配置ID\r\n @return 参数配置信息\r\n"}, {"name": "selectConfigByKey", "paramTypes": ["java.lang.String"], "doc": "\n 根据键名查询参数配置信息\r\n\r\n @param configKey 参数键名\r\n @return 参数键值\r\n"}, {"name": "selectRegisterEnabled", "paramTypes": ["java.lang.String"], "doc": "\n 获取注册开关\r\n @param tenantId 租户id\r\n @return true开启，false关闭\r\n"}, {"name": "selectConfigList", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": "\n 查询参数配置列表\r\n\r\n @param config 参数配置信息\r\n @return 参数配置集合\r\n"}, {"name": "insertConfig", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": "\n 新增参数配置\r\n\r\n @param bo 参数配置信息\r\n @return 结果\r\n"}, {"name": "updateConfig", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": "\n 修改参数配置\r\n\r\n @param bo 参数配置信息\r\n @return 结果\r\n"}, {"name": "deleteConfigByIds", "paramTypes": ["java.util.List"], "doc": "\n 批量删除参数信息\r\n\r\n @param configIds 需要删除的参数ID\r\n"}, {"name": "resetConfigCache", "paramTypes": [], "doc": "\n 重置参数缓存数据\r\n"}, {"name": "checkConfigKeyUnique", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": "\n 校验参数键名是否唯一\r\n\r\n @param config 参数信息\r\n @return 结果\r\n"}], "constructors": []}