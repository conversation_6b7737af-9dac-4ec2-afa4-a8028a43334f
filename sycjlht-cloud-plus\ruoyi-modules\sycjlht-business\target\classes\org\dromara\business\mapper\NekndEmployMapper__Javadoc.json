{"doc": "\n 招聘岗位Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-05-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndEmployById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询招聘岗位\r\n\r\n @param id 招聘岗位主键\r\n @return 招聘岗位\r\n"}, {"name": "selectNekndEmployList", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": "\n 查询招聘岗位列表\r\n\r\n @param nekndEmploy 招聘岗位\r\n @return 招聘岗位集合\r\n"}, {"name": "selectNekndEmployListReview", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": "\n 审核查询招聘岗位列表\r\n\r\n @param nekndEmploy 招聘岗位\r\n @return 招聘岗位集合\r\n"}, {"name": "insertNekndEmploy", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": "\n 新增招聘岗位\r\n\r\n @param nekndEmploy 招聘岗位\r\n @return 结果\r\n"}, {"name": "updateNekndEmploy", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": "\n 修改招聘岗位\r\n\r\n @param nekndEmploy 招聘岗位\r\n @return 结果\r\n"}, {"name": "deleteNekndEmployById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除招聘岗位\r\n\r\n @param id 招聘岗位主键\r\n @return 结果\r\n"}, {"name": "deleteNekndEmployByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除招聘岗位\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}