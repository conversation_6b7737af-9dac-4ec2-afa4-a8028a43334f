{"doc": "\n 技术标准立项公告信息Controller\r\n \r\n <AUTHOR>\r\n @date 2024-12-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询技术标准立项公告信息列表\r\n"}, {"name": "adminList", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 管理端查询技术标准立项公告信息列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": "\n 导出技术标准立项公告信息列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取技术标准立项公告信息详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": "\n 新增技术标准立项公告信息\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": "\n 修改技术标准立项公告信息\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除技术标准立项公告信息\r\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 批量更新状态\r\n"}, {"name": "getLatestProjects", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取最新的技术标准立项公告\r\n"}, {"name": "filterByStatus", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 按状态筛选技术标准项目\r\n"}], "constructors": []}