package org.dromara.common.log.event;

import io.github.linpeilie.AutoMapperConfig__960;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBo;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper__14;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__960.class,
    uses = {SysOperLogBoToSysOperLogMapper__14.class,SysOperLogBoToOperLogEventMapper__14.class},
    imports = {}
)
public interface OperLogEventToSysOperLogBoMapper__14 extends BaseMapper<OperLogEvent, SysOperLogBo> {
}
