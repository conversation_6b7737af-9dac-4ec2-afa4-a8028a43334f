{"doc": "\n 通用http发送方法\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendGet", "paramTypes": ["java.lang.String"], "doc": "\n 向指定 URL 发送GET方法的请求\r\n\r\n @param url 发送请求的 URL\r\n @return 所代表远程资源的响应结果\r\n"}, {"name": "sendGet", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 向指定 URL 发送GET方法的请求\r\n\r\n @param url   发送请求的 URL\r\n @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。\r\n @return 所代表远程资源的响应结果\r\n"}, {"name": "sendGet", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 向指定 URL 发送GET方法的请求\r\n\r\n @param url         发送请求的 URL\r\n @param param       请求参数，请求参数应该是 name1=value1&name2=value2 的形式。\r\n @param contentType 编码类型\r\n @return 所代表远程资源的响应结果\r\n"}, {"name": "sendPost", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 向指定 URL 发送POST方法的请求\r\n\r\n @param url   发送请求的 URL\r\n @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。\r\n @return 所代表远程资源的响应结果\r\n"}, {"name": "getHttpClient", "paramTypes": [], "doc": "\n 获取httpClient\r\n\r\n @return\r\n"}, {"name": "createConnectionManager", "paramTypes": [], "doc": "\n 创建连接池管理器\r\n\r\n @return\r\n"}, {"name": "createRequestConfig", "paramTypes": [], "doc": "\n 根据当前配置创建HTTP请求配置参数。\r\n\r\n @return 返回HTTP请求配置。\r\n"}, {"name": "createHttpClient", "paramTypes": ["org.apache.http.conn.HttpClientConnectionManager"], "doc": "\n 创建默认的HTTPS客户端，信任所有的证书。\r\n\r\n @return 返回HTTPS客户端，如果创建失败，返回HTTP客户端。\r\n"}, {"name": "initClient", "paramTypes": [], "doc": "\n 初始化 只需调用一次\r\n"}, {"name": "shutdown", "paramTypes": [], "doc": "\n 关闭HTTP客户端。\r\n"}, {"name": "getCall", "paramTypes": ["java.lang.String"], "doc": "\n 请求上游 GET提交\r\n\r\n @param uri\r\n @throws IOException\r\n"}, {"name": "getCall", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 请求上游 GET提交\r\n\r\n @param uri\r\n @param contentType\r\n @throws IOException\r\n"}, {"name": "getCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 请求上游 GET提交\r\n\r\n @param uri\r\n @param contentType\r\n @param charsetName\r\n @throws IOException\r\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 请求上游 POST提交\r\n\r\n @param uri\r\n @param paramsMap\r\n @throws IOException\r\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": "\n 请求上游 POST提交\r\n\r\n @param uri\r\n @param contentType\r\n @param paramsMap\r\n @throws IOException\r\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.String"], "doc": "\n 请求上游 POST提交\r\n\r\n @param uri\r\n @param contentType\r\n @param paramsMap\r\n @param charsetName\r\n @throws IOException\r\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 请求上游 POST提交\r\n\r\n @param uri\r\n @param param\r\n @throws IOException\r\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 请求上游 POST提交\r\n\r\n @param uri\r\n @param contentType\r\n @param param\r\n @throws IOException\r\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 请求上游 POST提交\r\n\r\n @param uri\r\n @param contentType\r\n @param param\r\n @param charsetName\r\n @throws IOException\r\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.util.Map"], "doc": "\n 请求上游 POST提交，支持自定义请求头\r\n\r\n @param uri         请求地址\r\n @param contentType 内容类型\r\n @param param       请求参数\r\n @param charsetName 字符集\r\n @param token       认证token\r\n @param headers     自定义请求头\r\n @throws Exception\r\n"}, {"name": "isReadTimeout", "paramTypes": ["java.lang.Throwable"], "doc": "\n 判断HTTP异常是否为读取超时。\r\n\r\n @param e 异常对象。\r\n @return 如果是读取引起的异常（而非连接），则返回true；否则返回false。\r\n"}, {"name": "isCausedBy", "paramTypes": ["java.lang.Throwable", "java.lang.Class"], "doc": "\n 检测异常e被触发的原因是不是因为异常cause。检测被封装的异常。\r\n\r\n @param e     捕获的异常。\r\n @param cause 异常触发原因。\r\n @return 如果异常e是由cause类异常触发，则返回true；否则返回false。\r\n"}], "constructors": []}