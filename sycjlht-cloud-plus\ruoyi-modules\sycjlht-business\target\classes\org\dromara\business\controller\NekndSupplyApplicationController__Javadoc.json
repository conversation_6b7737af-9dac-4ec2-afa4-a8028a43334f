{"doc": "\n 企业数字化应用Controller\r\n \r\n <AUTHOR>\r\n @date 2024-05-13\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndSupplyApplication", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询企业数字化应用列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndSupplyApplication"], "doc": "\n 导出企业数字化应用列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取企业数字化应用详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndSupplyApplication"], "doc": "\n 新增企业数字化应用\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndSupplyApplication"], "doc": "\n 修改企业数字化应用\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除企业数字化应用\r\n"}], "constructors": []}