package org.dromara.business.controller;

import java.util.List;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.annotation.SaIgnore;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.business.domain.NekndProvincialCity;
import org.dromara.business.service.INekndProvincialCityService;
import org.dromara.system.api.RemoteUserService;

/**
 * 市级管理Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/provincialCity")
public class NekndProvincialCityController extends BaseController {

    private final INekndProvincialCityService nekndProvincialCityService;
    
    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 获取城市列表（湖北省咸宁市优先显示）
     */
    @SaIgnore
    @GetMapping("/getList")
    public R<List<NekndProvincialCity>> getList(NekndProvincialCity nekndProvincialCity) {
        List<NekndProvincialCity> list = nekndProvincialCityService.selectNekndProvincialCityList(nekndProvincialCity);
        
        // 特殊处理：当查询湖北省(pid=18)时，将咸宁市放在第一位（索引10）
        if (list != null && list.size() > 10 && 
            nekndProvincialCity != null && 
            nekndProvincialCity.getPid() != null && 
            nekndProvincialCity.getPid() == 18L) {
            NekndProvincialCity xianning = list.remove(10);
            list.add(0, xianning);
        }
        
        return R.ok(list);
    }

    /**
     * 获取市级详细信息
     */
    @SaIgnore
    @GetMapping("/{cid}")
    public R<NekndProvincialCity> getInfo(@NotNull(message = "城市ID不能为空") @PathVariable Long cid) {
        NekndProvincialCity city = nekndProvincialCityService.selectNekndProvincialCityByCid(cid);
        return R.ok(city);
    }

    /**
     * 查询城市列表（分页）
     */
    @SaIgnore
    @GetMapping("/list")
    public TableDataInfo<NekndProvincialCity> list(NekndProvincialCity nekndProvincialCity, PageQuery pageQuery) {
        return nekndProvincialCityService.queryPageList(nekndProvincialCity, pageQuery);
    }

    /**
     * 按省份ID查询城市
     */
    @SaIgnore
    @GetMapping("/byProvince/{pid}")
    public R<List<NekndProvincialCity>> getByProvince(@NotNull(message = "省份ID不能为空") @PathVariable Long pid) {
        NekndProvincialCity query = new NekndProvincialCity();
        query.setPid(pid);
        List<NekndProvincialCity> list = nekndProvincialCityService.selectNekndProvincialCityList(query);
        return R.ok(list);
    }

    /**
     * 搜索城市
     */
    @SaIgnore
    @GetMapping("/search")
    public R<List<NekndProvincialCity>> search(@RequestParam String keyword) {
        List<NekndProvincialCity> list = nekndProvincialCityService.searchCities(keyword);
        return R.ok(list);
    }

    /**
     * 获取城市统计信息
     */
    @SaIgnore
    @GetMapping("/statistics")
    public R<Object> getStatistics() {
        return R.ok(nekndProvincialCityService.getCityStatistics());
    }

    /**
     * 获取热门城市
     */
    @SaIgnore
    @GetMapping("/popular")
    public R<List<NekndProvincialCity>> getPopular() {
        List<NekndProvincialCity> list = nekndProvincialCityService.getPopularCities();
        return R.ok(list);
    }
}
