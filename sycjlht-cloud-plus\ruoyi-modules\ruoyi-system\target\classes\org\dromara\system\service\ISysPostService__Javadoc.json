{"doc": "\n 岗位信息 服务层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPostList", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": "\n 查询岗位信息集合\r\n\r\n @param post 岗位信息\r\n @return 岗位列表\r\n"}, {"name": "selectPostsByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户所属岗位组\r\n\r\n @param userId 用户ID\r\n @return 岗位ID\r\n"}, {"name": "selectPostAll", "paramTypes": [], "doc": "\n 查询所有岗位\r\n\r\n @return 岗位列表\r\n"}, {"name": "selectPostById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过岗位ID查询岗位信息\r\n\r\n @param postId 岗位ID\r\n @return 角色对象信息\r\n"}, {"name": "selectPostListByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID获取岗位选择框列表\r\n\r\n @param userId 用户ID\r\n @return 选中岗位ID列表\r\n"}, {"name": "selectPostByIds", "paramTypes": ["java.util.List"], "doc": "\n 通过岗位ID串查询岗位\r\n\r\n @param postIds 岗位id串\r\n @return 岗位列表信息\r\n"}, {"name": "checkPostNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": "\n 校验岗位名称\r\n\r\n @param post 岗位信息\r\n @return 结果\r\n"}, {"name": "checkPostCodeUnique", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": "\n 校验岗位编码\r\n\r\n @param post 岗位信息\r\n @return 结果\r\n"}, {"name": "countUserPostById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过岗位ID查询岗位使用数量\r\n\r\n @param postId 岗位ID\r\n @return 结果\r\n"}, {"name": "countPostByDeptId", "paramTypes": ["java.lang.Long"], "doc": "\n 通过部门ID查询岗位使用数量\r\n\r\n @param deptId 部门id\r\n @return 结果\r\n"}, {"name": "deletePostById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除岗位信息\r\n\r\n @param postId 岗位ID\r\n @return 结果\r\n"}, {"name": "deletePostByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除岗位信息\r\n\r\n @param postIds 需要删除的岗位ID\r\n @return 结果\r\n"}, {"name": "insertPost", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": "\n 新增保存岗位信息\r\n\r\n @param bo 岗位信息\r\n @return 结果\r\n"}, {"name": "updatePost", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": "\n 修改保存岗位信息\r\n\r\n @param bo 岗位信息\r\n @return 结果\r\n"}], "constructors": []}