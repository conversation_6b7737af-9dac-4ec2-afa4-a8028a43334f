{"doc": "\n 企业人才收藏关系（人才收藏岗位，企业收藏人才）Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-04-14\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndFavoritesById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n\r\n @param id 企业人才收藏关系（人才收藏岗位，企业收藏人才）主键\r\n @return 企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n"}, {"name": "selectNekndFavoritesList", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": "\n 查询企业人才收藏关系（人才收藏岗位，企业收藏人才）列表\r\n\r\n @param nekndFavorites 企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n @return 企业人才收藏关系（人才收藏岗位，企业收藏人才）集合\r\n"}, {"name": "insertNekndFavorites", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": "\n 新增企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n\r\n @param nekndFavorites 企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n @return 结果\r\n"}, {"name": "updateNekndFavorites", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": "\n 修改企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n\r\n @param nekndFavorites 企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n @return 结果\r\n"}, {"name": "deleteNekndFavoritesById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n\r\n @param id 企业人才收藏关系（人才收藏岗位，企业收藏人才）主键\r\n @return 结果\r\n"}, {"name": "deleteNekndFavoritesByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除企业人才收藏关系（人才收藏岗位，企业收藏人才）\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}, {"name": "getCountFavorite", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": "\n 根据用户id和目标id和目标类型查询收藏记录数量\r\n @param userId\r\n @param targetId\r\n @param targetType\r\n @return\r\n"}, {"name": "isFavorite", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": "\n 检查用户是否收藏了指定目标\r\n @param userId 用户ID\r\n @param targetId 目标ID\r\n @param targetType 目标类型\r\n @return 是否收藏\r\n"}], "constructors": []}