{"doc": "\n 办理人权限处理器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "permissions", "paramTypes": [], "doc": "\n 办理人权限标识，比如用户，角色，部门等，用于校验是否有权限办理任务\r\n 后续在{@link FlowParams#getPermissionFlag}  中获取\r\n 返回当前用户权限集合\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": "\n 获取当前办理人\r\n\r\n @return 当前办理人\r\n"}, {"name": "convertPermissions", "paramTypes": ["java.util.List"], "doc": "\n 转换办理人，比如设计器中预设了能办理的人，如果其中包含角色或者部门id等，可以通过此接口进行转换成用户id\r\n"}], "constructors": []}