{"doc": "\n 报告信息管理Controller\r\n \r\n <AUTHOR>\r\n @date 2024-09-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndReportInformation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询报告信息列表\r\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndReportInformation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 门户查询报告信息列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndReportInformation"], "doc": "\n 导出报告信息列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取报告信息详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndReportInformation"], "doc": "\n 新增报告信息\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndReportInformation"], "doc": "\n 修改报告信息\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除报告信息\r\n"}, {"name": "applyAccess", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": "\n 申请报告访问权限\r\n"}, {"name": "auditAccess", "paramTypes": ["java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": "\n 审核报告访问权限申请\r\n"}, {"name": "getMyPrivileges", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取用户的权限申请记录\r\n"}], "constructors": []}