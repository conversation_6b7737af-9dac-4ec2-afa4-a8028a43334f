package org.dromara.system.domain.convert;

import javax.annotation.processing.Generated;
import org.dromara.system.api.domain.vo.RemoteDictDataVo;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:06:26+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Oracle Corporation)"
)
@Component
public class SysDictDataVoConvertImpl implements SysDictDataVoConvert {

    @Override
    public RemoteDictDataVo convert(SysDictDataVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        RemoteDictDataVo remoteDictDataVo = new RemoteDictDataVo();

        remoteDictDataVo.setDictCode( arg0.getDictCode() );
        remoteDictDataVo.setDictSort( arg0.getDictSort() );
        remoteDictDataVo.setDictLabel( arg0.getDictLabel() );
        remoteDictDataVo.setDictValue( arg0.getDictValue() );
        remoteDictDataVo.setDictType( arg0.getDictType() );
        remoteDictDataVo.setCssClass( arg0.getCssClass() );
        remoteDictDataVo.setListClass( arg0.getListClass() );
        remoteDictDataVo.setIsDefault( arg0.getIsDefault() );
        remoteDictDataVo.setRemark( arg0.getRemark() );
        remoteDictDataVo.setCreateTime( arg0.getCreateTime() );

        return remoteDictDataVo;
    }

    @Override
    public RemoteDictDataVo convert(SysDictDataVo arg0, RemoteDictDataVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setDictCode( arg0.getDictCode() );
        arg1.setDictSort( arg0.getDictSort() );
        arg1.setDictLabel( arg0.getDictLabel() );
        arg1.setDictValue( arg0.getDictValue() );
        arg1.setDictType( arg0.getDictType() );
        arg1.setCssClass( arg0.getCssClass() );
        arg1.setListClass( arg0.getListClass() );
        arg1.setIsDefault( arg0.getIsDefault() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
