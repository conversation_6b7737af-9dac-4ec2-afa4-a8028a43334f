{"doc": "\n 字典表 数据层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectDictLabel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典键值查询字典标签\r\n\r\n @param dictType  字典类型\r\n @param dictValue 字典键值\r\n @return 字典标签\r\n"}, {"name": "selectDictRemark", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典键值查询字典备注\r\n\r\n @param dictType  字典类型\r\n @param dictValue 字典键值\r\n @return 字典备注\r\n"}], "constructors": []}