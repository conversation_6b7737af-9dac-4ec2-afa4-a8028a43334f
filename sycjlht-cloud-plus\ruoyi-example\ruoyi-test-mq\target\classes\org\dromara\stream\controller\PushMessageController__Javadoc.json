{"doc": "\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "rabbitSend", "paramTypes": [], "doc": "\n rabbitmq 普通消息\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["long"], "doc": "\n rabbitmq 延迟队列消息\r\n"}, {"name": "rocketSend", "paramTypes": [], "doc": "\n rocketmq 发送消息\r\n 需要手动创建相关的Topic和group\r\n"}, {"name": "rocketTransaction", "paramTypes": [], "doc": "\n rocketmq 事务消息\r\n"}, {"name": "kafkaSend", "paramTypes": [], "doc": "\n kafka 发送消息\r\n"}], "constructors": []}