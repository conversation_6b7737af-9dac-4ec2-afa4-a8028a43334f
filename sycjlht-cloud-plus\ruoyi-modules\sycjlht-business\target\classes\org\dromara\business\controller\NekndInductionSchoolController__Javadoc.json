{"doc": "\n 入学管理Controller\r\n \r\n <AUTHOR>\r\n @date 2024-12-26\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getCollegeChartData", "paramTypes": [], "doc": "\n 获取学院图表数据\r\n"}, {"name": "getByCollegeDataList", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取学院数据列表\r\n"}, {"name": "list", "paramTypes": ["org.dromara.business.domain.NekndInductionSchool", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询入学管理列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndInductionSchool"], "doc": "\n 导出入学管理列表\r\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 导入入学信息\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取入学管理详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndInductionSchool"], "doc": "\n 新增入学管理\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndInductionSchool"], "doc": "\n 修改入学管理\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除入学管理\r\n"}, {"name": "audit", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": "\n 审核入学申请\r\n"}, {"name": "getStatistics", "paramTypes": [], "doc": "\n 获取入学统计信息\r\n"}, {"name": "getByYear", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 按入学年份查询\r\n"}, {"name": "getMyApplications", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取当前用户的入学申请\r\n"}], "constructors": []}