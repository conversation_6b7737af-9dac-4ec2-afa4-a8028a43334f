{"doc": "\n 课程管理控制器\r\n\r\n <AUTHOR>\r\n @date 2024-12-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCourses", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询课程列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCourses"], "doc": "\n 导出课程列表\r\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 导入课程数据\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取课程详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCourses"], "doc": "\n 新增课程\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCourses"], "doc": "\n 修改课程\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除课程\r\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndCourses", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 门户查询课程列表\r\n"}, {"name": "getInfoByCourseId", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 门户获取课程详细信息\r\n"}], "constructors": []}