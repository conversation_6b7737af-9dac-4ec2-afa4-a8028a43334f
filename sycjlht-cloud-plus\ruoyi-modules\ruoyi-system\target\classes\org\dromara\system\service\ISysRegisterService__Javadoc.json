{"doc": " 用户注册服务接口\n\n <AUTHOR>\n @date 2024-06-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "register", "paramTypes": ["org.dromara.system.domain.bo.RegisterBody"], "doc": " 用户注册\n\n @param registerBody 注册信息\n @return 注册结果消息，null或空字符串表示成功，其他表示失败原因\n"}, {"name": "checkUsernameUnique", "paramTypes": ["java.lang.String"], "doc": " 检查用户名是否唯一\n\n @param username 用户名\n @return true表示可用，false表示已存在\n"}, {"name": "checkPhoneUnique", "paramTypes": ["java.lang.String"], "doc": " 检查手机号是否唯一\n\n @param phonenumber 手机号\n @return true表示可用，false表示已存在\n"}], "constructors": []}