{"doc": "\n 继续教育报名记录Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-10-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndJuniorcollegeUpgradeUndergraduateRecordsById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询继续教育报名记录\r\n\r\n @param id 继续教育报名记录主键\r\n @return 继续教育报名记录\r\n"}, {"name": "selectNekndJuniorcollegeUpgradeUndergraduateRecordsList", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": "\n 查询继续教育报名记录列表\r\n\r\n @param nekndJuniorcollegeUpgradeUndergraduateRecords 继续教育报名记录\r\n @return 继续教育报名记录\r\n"}, {"name": "insertNekndJuniorcollegeUpgradeUndergraduateRecords", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": "\n 新增继续教育报名记录\r\n\r\n @param nekndJuniorcollegeUpgradeUndergraduateRecords 继续教育报名记录\r\n @return 结果\r\n"}, {"name": "updateNekndJuniorcollegeUpgradeUndergraduateRecords", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": "\n 修改继续教育报名记录\r\n\r\n @param nekndJuniorcollegeUpgradeUndergraduateRecords 继续教育报名记录\r\n @return 结果\r\n"}, {"name": "deleteNekndJuniorcollegeUpgradeUndergraduateRecordsByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除继续教育报名记录\r\n\r\n @param ids 需要删除的继续教育报名记录主键\r\n @return 结果\r\n"}, {"name": "deleteNekndJuniorcollegeUpgradeUndergraduateRecordsById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除继续教育报名记录信息\r\n\r\n @param id 继续教育报名记录主键\r\n @return 结果\r\n"}, {"name": "selectNekndJuniorcollegeUpgradeUndergraduateRecordsListForExport", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": "\n 查询导出用的继续教育报名记录列表\r\n"}], "constructors": []}