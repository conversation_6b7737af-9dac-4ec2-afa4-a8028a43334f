{"doc": "\n 培训报名Controller\r\n \r\n <AUTHOR>\r\n @date 2024-06-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询培训报名列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 导出培训报名列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取培训报名详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 新增培训报名\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 修改培训报名\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除培训报名（软删除）\r\n"}, {"name": "registration", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 报名培训\r\n"}, {"name": "getRegistrationStatus", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 查询报名状态\r\n"}, {"name": "selectPersonalTraining", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 个人培训项目查看\r\n"}, {"name": "selectEnterpriseTraining", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 企业培训项目查看\r\n"}, {"name": "cancelRegistration", "paramTypes": ["java.lang.Integer"], "doc": "\n 取消报名\r\n"}, {"name": "getMyRegistrations", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取当前用户的报名记录\r\n"}, {"name": "batch<PERSON><PERSON><PERSON>", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 批量审核报名\r\n"}], "constructors": []}