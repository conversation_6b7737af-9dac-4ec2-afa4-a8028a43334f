{"doc": "\n 历史任务视图\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}, {"name": "tenantId", "doc": "\n 租户ID\r\n"}, {"name": "delFlag", "doc": "\n 删除标记\r\n"}, {"name": "definitionId", "doc": "\n 对应flow_definition表的id\r\n"}, {"name": "flowName", "doc": "\n 流程定义名称\r\n"}, {"name": "instanceId", "doc": "\n 流程实例表id\r\n"}, {"name": "taskId", "doc": "\n 任务表id\r\n"}, {"name": "cooperateType", "doc": "\n 协作方式(1审批 2转办 3委派 4会签 5票签 6加签 7减签)\r\n"}, {"name": "cooperateTypeName", "doc": "\n 协作方式(1审批 2转办 3委派 4会签 5票签 6加签 7减签)\r\n"}, {"name": "businessId", "doc": "\n 业务id\r\n"}, {"name": "nodeCode", "doc": "\n 开始节点编码\r\n"}, {"name": "nodeName", "doc": "\n 开始节点名称\r\n"}, {"name": "nodeType", "doc": "\n 开始节点类型（0开始节点 1中间节点 2结束节点 3互斥网关 4并行网关）\r\n"}, {"name": "targetNodeCode", "doc": "\n 目标节点编码\r\n"}, {"name": "targetNodeName", "doc": "\n 结束节点名称\r\n"}, {"name": "approver", "doc": "\n 审批者\r\n"}, {"name": "approveName", "doc": "\n 审批者\r\n"}, {"name": "collaborator", "doc": "\n 协作人(只有转办、会签、票签、委派)\r\n"}, {"name": "permissionList", "doc": "\n 权限标识 permissionFlag的list形式\r\n"}, {"name": "skipType", "doc": "\n 跳转类型（PASS通过 REJECT退回 NONE无动作）\r\n"}, {"name": "flowStatus", "doc": "\n 流程状态\r\n"}, {"name": "flowTaskStatus", "doc": "\n 任务状态\r\n"}, {"name": "flowStatusName", "doc": "\n 流程状态\r\n"}, {"name": "message", "doc": "\n 审批意见\r\n"}, {"name": "ext", "doc": "\n 业务详情 存业务类的json\r\n"}, {"name": "createBy", "doc": "\n 创建者\r\n"}, {"name": "createByName", "doc": "\n 申请人\r\n"}, {"name": "category", "doc": "\n 流程分类id\r\n"}, {"name": "categoryName", "doc": "\n 流程分类名称\r\n"}, {"name": "formCustom", "doc": "\n 审批表单是否自定义（Y是 N否）\r\n"}, {"name": "formPath", "doc": "\n 审批表单路径\r\n"}, {"name": "flowCode", "doc": "\n 流程定义编码\r\n"}, {"name": "version", "doc": "\n 流程版本号\r\n"}, {"name": "runDuration", "doc": "\n 运行时长\r\n"}], "enumConstants": [], "methods": [{"name": "setCreateTime", "paramTypes": ["java.util.Date"], "doc": "\n 设置创建时间并计算任务运行时长\r\n\r\n @param createTime 创建时间\r\n"}, {"name": "setUpdateTime", "paramTypes": ["java.util.Date"], "doc": "\n 设置更新时间并计算任务运行时长\r\n\r\n @param updateTime 更新时间\r\n"}, {"name": "updateRunDuration", "paramTypes": [], "doc": "\n 更新运行时长\r\n"}, {"name": "setCooperateType", "paramTypes": ["java.lang.Integer"], "doc": "\n 设置协作方式，并通过协作方式获取名称\r\n"}], "constructors": []}