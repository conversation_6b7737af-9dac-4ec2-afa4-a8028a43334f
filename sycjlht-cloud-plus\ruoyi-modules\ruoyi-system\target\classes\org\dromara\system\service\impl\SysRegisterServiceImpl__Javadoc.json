{"doc": "\n 用户注册服务实现\r\n\r\n <AUTHOR>\r\n @date 2024-06-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "register", "paramTypes": ["org.dromara.system.domain.bo.RegisterBody"], "doc": "\n 用户注册\r\n"}, {"name": "checkUsernameUnique", "paramTypes": ["java.lang.String"], "doc": "\n 检查用户名是否唯一\r\n"}, {"name": "checkPhoneUnique", "paramTypes": ["java.lang.String"], "doc": "\n 检查手机号是否唯一\r\n"}], "constructors": []}