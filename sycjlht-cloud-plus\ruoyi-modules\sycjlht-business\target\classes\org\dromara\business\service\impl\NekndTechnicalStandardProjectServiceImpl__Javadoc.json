{"doc": " 技术标准立项公告信息Service业务层处理\n\n <AUTHOR>\n @date 2024-12-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndTechnicalStandardProjectById", "paramTypes": ["java.lang.Integer"], "doc": " 查询技术标准立项公告信息\n\n @param id 技术标准立项公告信息主键\n @return 技术标准立项公告信息\n"}, {"name": "selectNekndTechnicalStandardProjectList", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": " 查询技术标准立项公告信息列表\n\n @param nekndTechnicalStandardProject 技术标准立项公告信息\n @return 技术标准立项公告信息\n"}, {"name": "selectPageNekndTechnicalStandardProjectList", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询技术标准立项公告信息列表\n\n @param nekndTechnicalStandardProject 技术标准立项公告信息\n @param pageQuery                     分页参数\n @return 技术标准立项公告信息集合\n"}, {"name": "buildQueryWrapper", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": " 构建查询条件\n"}, {"name": "insertNekndTechnicalStandardProject", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": " 新增技术标准立项公告信息\n\n @param nekndTechnicalStandardProject 技术标准立项公告信息\n @return 结果\n"}, {"name": "updateNekndTechnicalStandardProject", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": " 修改技术标准立项公告信息\n\n @param nekndTechnicalStandardProject 技术标准立项公告信息\n @return 结果\n"}, {"name": "deleteNekndTechnicalStandardProjectByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除技术标准立项公告信息\n\n @param ids 需要删除的技术标准立项公告信息主键\n @return 结果\n"}, {"name": "deleteNekndTechnicalStandardProjectById", "paramTypes": ["java.lang.Integer"], "doc": " 删除技术标准立项公告信息信息\n\n @param id 技术标准立项公告信息主键\n @return 结果\n"}], "constructors": []}