package org.dromara.system.domain.vo;

import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.NekndPerson;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:06:28+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Oracle Corporation)"
)
@Component
public class NekndPersonVoToNekndPersonMapperImpl implements NekndPersonVoToNekndPersonMapper {

    @Override
    public NekndPerson convert(NekndPersonVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NekndPerson nekndPerson = new NekndPerson();

        if ( arg0.getCreateTime() != null ) {
            nekndPerson.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        if ( arg0.getUpdateTime() != null ) {
            nekndPerson.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        nekndPerson.setTenantId( arg0.getTenantId() );
        nekndPerson.setId( arg0.getId() );
        nekndPerson.setUserId( arg0.getUserId() );
        nekndPerson.setPictureUri( arg0.getPictureUri() );
        nekndPerson.setName( arg0.getName() );
        nekndPerson.setSex( arg0.getSex() );
        nekndPerson.setAddress( arg0.getAddress() );
        nekndPerson.setPhone( arg0.getPhone() );
        nekndPerson.setEmail( arg0.getEmail() );
        nekndPerson.setWorkYear( arg0.getWorkYear() );
        nekndPerson.setPositionStatus( arg0.getPositionStatus() );
        nekndPerson.setTechnical( arg0.getTechnical() );
        nekndPerson.setExpectedPosition( arg0.getExpectedPosition() );
        nekndPerson.setExpectedMoney( arg0.getExpectedMoney() );
        nekndPerson.setJobType( arg0.getJobType() );
        nekndPerson.setWorkExperienceJson( arg0.getWorkExperienceJson() );
        nekndPerson.setEducationalBackgroundJson( arg0.getEducationalBackgroundJson() );
        nekndPerson.setProvincialName( arg0.getProvincialName() );
        nekndPerson.setCityName( arg0.getCityName() );
        nekndPerson.setCompanyDeptName( arg0.getCompanyDeptName() );
        nekndPerson.setSchoolDeptName( arg0.getSchoolDeptName() );
        nekndPerson.setPendingApprovalCompanyDeptId( arg0.getPendingApprovalCompanyDeptId() );
        nekndPerson.setPendingApprovalSchoolDeptId( arg0.getPendingApprovalSchoolDeptId() );
        nekndPerson.setPendingClassify( arg0.getPendingClassify() );
        nekndPerson.setClassify( arg0.getClassify() );
        nekndPerson.setEducationStatus( arg0.getEducationStatus() );
        nekndPerson.setProfessionalTitles( arg0.getProfessionalTitles() );
        nekndPerson.setExpertsTypes( arg0.getExpertsTypes() );
        nekndPerson.setStatus( arg0.getStatus() );
        nekndPerson.setAppointment( arg0.getAppointment() );
        nekndPerson.setAppointmentTime( arg0.getAppointmentTime() );
        nekndPerson.setPersonalExperience( arg0.getPersonalExperience() );
        nekndPerson.setResume( arg0.getResume() );
        nekndPerson.setCertificateHonorUri( arg0.getCertificateHonorUri() );
        nekndPerson.setIsTop( arg0.getIsTop() );
        nekndPerson.setUpdateStatus( arg0.getUpdateStatus() );

        return nekndPerson;
    }

    @Override
    public NekndPerson convert(NekndPersonVo arg0, NekndPerson arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setTenantId( arg0.getTenantId() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setPictureUri( arg0.getPictureUri() );
        arg1.setName( arg0.getName() );
        arg1.setSex( arg0.getSex() );
        arg1.setAddress( arg0.getAddress() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setWorkYear( arg0.getWorkYear() );
        arg1.setPositionStatus( arg0.getPositionStatus() );
        arg1.setTechnical( arg0.getTechnical() );
        arg1.setExpectedPosition( arg0.getExpectedPosition() );
        arg1.setExpectedMoney( arg0.getExpectedMoney() );
        arg1.setJobType( arg0.getJobType() );
        arg1.setWorkExperienceJson( arg0.getWorkExperienceJson() );
        arg1.setEducationalBackgroundJson( arg0.getEducationalBackgroundJson() );
        arg1.setProvincialName( arg0.getProvincialName() );
        arg1.setCityName( arg0.getCityName() );
        arg1.setCompanyDeptName( arg0.getCompanyDeptName() );
        arg1.setSchoolDeptName( arg0.getSchoolDeptName() );
        arg1.setPendingApprovalCompanyDeptId( arg0.getPendingApprovalCompanyDeptId() );
        arg1.setPendingApprovalSchoolDeptId( arg0.getPendingApprovalSchoolDeptId() );
        arg1.setPendingClassify( arg0.getPendingClassify() );
        arg1.setClassify( arg0.getClassify() );
        arg1.setEducationStatus( arg0.getEducationStatus() );
        arg1.setProfessionalTitles( arg0.getProfessionalTitles() );
        arg1.setExpertsTypes( arg0.getExpertsTypes() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setAppointment( arg0.getAppointment() );
        arg1.setAppointmentTime( arg0.getAppointmentTime() );
        arg1.setPersonalExperience( arg0.getPersonalExperience() );
        arg1.setResume( arg0.getResume() );
        arg1.setCertificateHonorUri( arg0.getCertificateHonorUri() );
        arg1.setIsTop( arg0.getIsTop() );
        arg1.setUpdateStatus( arg0.getUpdateStatus() );

        return arg1;
    }
}
