{"doc": " 通用 工作流服务实现\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "deleteInstance", "paramTypes": ["java.util.List"], "doc": " 删除流程实例\n\n @param businessIds 业务id\n @return 结果\n"}, {"name": "getBusinessStatusByTaskId", "paramTypes": ["java.lang.Long"], "doc": " 获取当前流程状态\n\n @param taskId 任务id\n"}, {"name": "getBusinessStatus", "paramTypes": ["java.lang.String"], "doc": " 获取当前流程状态\n\n @param businessId 业务id\n"}, {"name": "setVariable", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 设置流程变量\n\n @param instanceId 流程实例id\n @param variables  流程变量\n"}, {"name": "instanceVariable", "paramTypes": ["java.lang.Long"], "doc": " 获取流程变量\n\n @param instanceId 流程实例id\n"}, {"name": "getInstanceIdByBusinessId", "paramTypes": ["java.lang.String"], "doc": " 按照业务id查询流程实例id\n\n @param businessId 业务id\n @return 结果\n"}, {"name": "syncDef", "paramTypes": ["java.lang.String"], "doc": " 新增租户流程定义\n\n @param tenantId 租户id\n"}, {"name": "startWorkFlow", "paramTypes": ["org.dromara.workflow.api.domain.RemoteStartProcess"], "doc": " 启动流程\n\n @param startProcess 参数\n"}, {"name": "completeTask", "paramTypes": ["org.dromara.workflow.api.domain.RemoteCompleteTask"], "doc": " 办理任务\n 系统后台发起审批 无用户信息 需要忽略权限\n completeTask.getVariables().put(\"ignore\", true);\n\n @param completeTask 参数\n"}, {"name": "completeTask", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 办理任务\n\n @param taskId  任务ID\n @param message 办理意见\n"}], "constructors": []}