{"doc": "\n 通用 工作流服务实现\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "deleteInstance", "paramTypes": ["java.util.List"], "doc": "\n 删除流程实例\r\n\r\n @param businessIds 业务id\r\n @return 结果\r\n"}, {"name": "getBusinessStatusByTaskId", "paramTypes": ["java.lang.Long"], "doc": "\n 获取当前流程状态\r\n\r\n @param taskId 任务id\r\n"}, {"name": "getBusinessStatus", "paramTypes": ["java.lang.String"], "doc": "\n 获取当前流程状态\r\n\r\n @param businessId 业务id\r\n"}, {"name": "setVariable", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 设置流程变量\r\n\r\n @param instanceId 流程实例id\r\n @param variables  流程变量\r\n"}, {"name": "instanceVariable", "paramTypes": ["java.lang.Long"], "doc": "\n 获取流程变量\r\n\r\n @param instanceId 流程实例id\r\n"}, {"name": "getInstanceIdByBusinessId", "paramTypes": ["java.lang.String"], "doc": "\n 按照业务id查询流程实例id\r\n\r\n @param businessId 业务id\r\n @return 结果\r\n"}, {"name": "syncDef", "paramTypes": ["java.lang.String"], "doc": "\n 新增租户流程定义\r\n\r\n @param tenantId 租户id\r\n"}, {"name": "startWorkFlow", "paramTypes": ["org.dromara.workflow.api.domain.RemoteStartProcess"], "doc": "\n 启动流程\r\n\r\n @param startProcess 参数\r\n"}, {"name": "completeTask", "paramTypes": ["org.dromara.workflow.api.domain.RemoteCompleteTask"], "doc": "\n 办理任务\r\n 系统后台发起审批 无用户信息 需要忽略权限\r\n completeTask.getVariables().put(\"ignore\", true);\r\n\r\n @param completeTask 参数\r\n"}, {"name": "completeTask", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 办理任务\r\n\r\n @param taskId  任务ID\r\n @param message 办理意见\r\n"}], "constructors": []}