{"doc": "\n 流程实例视图\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}, {"name": "tenantId", "doc": "\n 租户ID\r\n"}, {"name": "delFlag", "doc": "\n 删除标记\r\n"}, {"name": "definitionId", "doc": "\n 对应flow_definition表的id\r\n"}, {"name": "flowName", "doc": "\n 流程定义名称\r\n"}, {"name": "flowCode", "doc": "\n 流程定义编码\r\n"}, {"name": "businessId", "doc": "\n 业务id\r\n"}, {"name": "nodeType", "doc": "\n 节点类型（0开始节点 1中间节点 2结束节点 3互斥网关 4并行网关）\r\n"}, {"name": "nodeCode", "doc": "\n 流程节点编码   每个流程的nodeCode是唯一的,即definitionId+nodeCode唯一,在数据库层面做了控制\r\n"}, {"name": "nodeName", "doc": "\n 流程节点名称\r\n"}, {"name": "variable", "doc": "\n 流程变量\r\n"}, {"name": "flowStatus", "doc": "\n 流程状态（0待提交 1审批中 2 审批通过 3自动通过 8已完成 9已退回 10失效）\r\n"}, {"name": "flowStatusName", "doc": "\n 流程状态\r\n"}, {"name": "activityStatus", "doc": "\n 流程激活状态（0挂起 1激活）\r\n"}, {"name": "formCustom", "doc": "\n 审批表单是否自定义（Y是 N否）\r\n"}, {"name": "formPath", "doc": "\n 审批表单路径\r\n"}, {"name": "ext", "doc": "\n 扩展字段，预留给业务系统使用\r\n"}, {"name": "version", "doc": "\n 流程定义版本\r\n"}, {"name": "createBy", "doc": "\n 创建者\r\n"}, {"name": "createByName", "doc": "\n 申请人\r\n"}, {"name": "category", "doc": "\n 流程分类id\r\n"}, {"name": "categoryName", "doc": "\n 流程分类名称\r\n"}], "enumConstants": [], "methods": [], "constructors": []}