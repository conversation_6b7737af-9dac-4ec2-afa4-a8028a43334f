{"doc": "\n 留言箱对象 neknd_message_box\r\n\r\n <AUTHOR>\r\n @date 2024-06-02\r\n", "fields": [{"name": "id", "doc": "ID "}, {"name": "delFlag", "doc": "删除标志（0代表存在 2代表删除） "}, {"name": "status", "doc": "状态(1:岗位需求,2:企业需求,3其他) "}, {"name": "recipientUserId", "doc": "收件用户id "}, {"name": "recipientUserName", "doc": "收件用户名称 "}, {"name": "recipientDeptId", "doc": "收件用户部门id "}, {"name": "content", "doc": "留言内容 "}, {"name": "title", "doc": "留言标题 "}, {"name": "readStatus", "doc": "已读状态(0:未读,1:已读) "}, {"name": "senderUserId", "doc": "发件用户id "}, {"name": "senderUserName", "doc": "发件用户名称 "}, {"name": "senderDeptId", "doc": "发件用户部门id "}, {"name": "avatar", "doc": "发送人用户头像地址 "}, {"name": "recipient<PERSON><PERSON><PERSON>", "doc": "收件人用户头像地址 "}, {"name": "phone", "doc": "发送人联系电话 "}, {"name": "email", "doc": "发送人联系邮箱 "}, {"name": "parentId", "doc": "留言主题id"}, {"name": "aiRecord", "doc": "前端用的字段 "}, {"name": "messageType", "doc": "消息类型 "}], "enumConstants": [], "methods": [{"name": "setTopicId", "paramTypes": ["java.lang.Integer"], "doc": "\n 设置主题ID（别名方法）\r\n @param topicId 主题ID\r\n"}, {"name": "setAiInterviewRecords", "paramTypes": ["java.util.List"], "doc": "\n 设置AI面试记录（别名方法）\r\n @param aiInterviewRecords AI面试记录\r\n"}], "constructors": []}