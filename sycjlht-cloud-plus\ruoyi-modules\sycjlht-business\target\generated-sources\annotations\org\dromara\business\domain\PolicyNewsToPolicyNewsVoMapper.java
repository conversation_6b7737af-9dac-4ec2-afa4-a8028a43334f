package org.dromara.business.domain;

import io.github.linpeilie.AutoMapperConfig__987;
import io.github.linpeilie.BaseMapper;
import org.dromara.business.domain.bo.PolicyNewsBoToPolicyNewsMapper;
import org.dromara.business.domain.vo.PolicyNewsVo;
import org.dromara.business.domain.vo.PolicyNewsVoToPolicyNewsMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__987.class,
    uses = {PolicyNewsVoToPolicyNewsMapper.class,PolicyNewsBoToPolicyNewsMapper.class},
    imports = {}
)
public interface PolicyNewsToPolicyNewsVoMapper extends BaseMapper<PolicyNews, PolicyNewsVo> {
}
