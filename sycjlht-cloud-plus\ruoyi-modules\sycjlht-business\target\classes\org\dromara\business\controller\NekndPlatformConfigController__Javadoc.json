{"doc": "\n 平台首页信息Controller\r\n \r\n <AUTHOR>\r\n @date 2025-02-05\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询平台首页信息列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndPlatformConfig"], "doc": "\n 导出平台首页信息列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取平台首页信息详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig"], "doc": "\n 新增平台首页信息\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig"], "doc": "\n 修改平台首页信息\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取平台首页信息详细信息（公共接口）\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.String"], "doc": "\n 获取平台首页信息详细信息（默认主题为0）\r\n"}, {"name": "clearCache", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 清除平台配置缓存\r\n"}, {"name": "clearAllCache", "paramTypes": [], "doc": "\n 批量清除缓存\r\n"}, {"name": "updateCache", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig"], "doc": "\n 更新缓存\r\n"}, {"name": "buildCacheKey", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 构建缓存Key\r\n"}], "constructors": []}