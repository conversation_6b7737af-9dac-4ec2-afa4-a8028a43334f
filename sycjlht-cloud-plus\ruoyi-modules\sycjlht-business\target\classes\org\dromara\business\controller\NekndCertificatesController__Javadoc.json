{"doc": "\n 证书发布Controller\r\n\r\n <AUTHOR>\r\n @date 2024-06-27\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCertificates", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询证书发布列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCertificates"], "doc": "\n 导出证书发布列表\r\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 导入证书发布\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载导入模板\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取证书发布详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCertificates"], "doc": "\n 新增证书发布\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCertificates"], "doc": "\n 修改证书发布\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除证书发布\r\n"}, {"name": "auditing", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": "\n 修改审核状态\r\n"}, {"name": "getPopularCertificates", "paramTypes": [], "doc": "\n 大屏接口\r\n 热门证书展示\r\n"}], "constructors": []}