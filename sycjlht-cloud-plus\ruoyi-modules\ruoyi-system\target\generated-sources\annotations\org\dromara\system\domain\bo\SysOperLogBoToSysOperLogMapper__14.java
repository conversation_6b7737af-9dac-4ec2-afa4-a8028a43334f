package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__960;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__14;
import org.dromara.system.domain.SysOperLog;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__960.class,
    uses = {SysOperLogBoToOperLogEventMapper__14.class,OperLogEventToSysOperLogBoMapper__14.class},
    imports = {}
)
public interface SysOperLogBoToSysOperLogMapper__14 extends BaseMapper<SysOperLogBo, SysOperLog> {
}
