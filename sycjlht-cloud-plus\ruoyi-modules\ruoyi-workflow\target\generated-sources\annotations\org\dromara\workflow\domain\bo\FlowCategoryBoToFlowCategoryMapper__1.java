package org.dromara.workflow.domain.bo;

import io.github.linpeilie.AutoMapperConfig__1005;
import io.github.linpeilie.BaseMapper;
import org.dromara.workflow.domain.FlowCategory;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1005.class,
    uses = {},
    imports = {}
)
public interface FlowCategoryBoToFlowCategoryMapper__1 extends BaseMapper<FlowCategoryBo, FlowCategory> {
}
