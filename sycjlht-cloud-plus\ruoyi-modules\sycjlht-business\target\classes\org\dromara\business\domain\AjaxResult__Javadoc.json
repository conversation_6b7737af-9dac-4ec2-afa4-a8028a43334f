{"doc": "\n 操作消息提醒\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "CODE_TAG", "doc": "状态码 "}, {"name": "MSG_TAG", "doc": "返回内容 "}, {"name": "DATA_TAG", "doc": "数据对象 "}], "enumConstants": [], "methods": [{"name": "success", "paramTypes": [], "doc": "\n 返回成功消息\r\n\r\n @return 成功消息\r\n"}, {"name": "success", "paramTypes": ["java.lang.Object"], "doc": "\n 返回成功数据\r\n\r\n @return 成功消息\r\n"}, {"name": "success", "paramTypes": ["java.lang.String"], "doc": "\n 返回成功消息\r\n\r\n @param msg 返回内容\r\n @return 成功消息\r\n"}, {"name": "success", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 返回成功消息\r\n\r\n @param msg 返回内容\r\n @param data 数据对象\r\n @return 成功消息\r\n"}, {"name": "warn", "paramTypes": ["java.lang.String"], "doc": "\n 返回警告消息\r\n\r\n @param msg 返回内容\r\n @return 警告消息\r\n"}, {"name": "warn", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 返回警告消息\r\n\r\n @param msg 返回内容\r\n @param data 数据对象\r\n @return 警告消息\r\n"}, {"name": "error", "paramTypes": [], "doc": "\n 返回错误消息\r\n\r\n @return 错误消息\r\n"}, {"name": "error", "paramTypes": ["java.lang.String"], "doc": "\n 返回错误消息\r\n\r\n @param msg 返回内容\r\n @return 错误消息\r\n"}, {"name": "error", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 返回错误消息\r\n\r\n @param msg 返回内容\r\n @param data 数据对象\r\n @return 错误消息\r\n"}, {"name": "error", "paramTypes": ["int", "java.lang.String"], "doc": "\n 返回错误消息\r\n\r\n @param code 状态码\r\n @param msg 返回内容\r\n @return 错误消息\r\n"}, {"name": "isSuccess", "paramTypes": [], "doc": "\n 是否为成功消息\r\n\r\n @return 结果\r\n"}, {"name": "is<PERSON><PERSON>n", "paramTypes": [], "doc": "\n 是否为警告消息\r\n\r\n @return 结果\r\n"}, {"name": "isError", "paramTypes": [], "doc": "\n 是否为错误消息\r\n\r\n @return 结果\r\n"}, {"name": "put", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 方便链式调用\r\n\r\n @param key 键\r\n @param value 值\r\n @return 数据对象\r\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "\n 初始化一个新创建的 AjaxResult 对象，使其表示一个空消息。\r\n"}, {"name": "<init>", "paramTypes": ["int", "java.lang.String"], "doc": "\n 初始化一个新创建的 AjaxResult 对象\r\n\r\n @param code 状态码\r\n @param msg 返回内容\r\n"}, {"name": "<init>", "paramTypes": ["int", "java.lang.String", "java.lang.Object"], "doc": "\n 初始化一个新创建的 AjaxResult 对象\r\n\r\n @param code 状态码\r\n @param msg 返回内容\r\n @param data 数据对象\r\n"}]}