{"doc": "\n 客户端工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getParameter", "paramTypes": ["java.lang.String"], "doc": "\n 获取String参数\r\n"}, {"name": "getParameter", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取String参数\r\n"}, {"name": "getParameterToInt", "paramTypes": ["java.lang.String"], "doc": "\n 获取Integer参数\r\n"}, {"name": "getParameterToInt", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取Integer参数\r\n"}, {"name": "getParameterToBool", "paramTypes": ["java.lang.String"], "doc": "\n 获取Boolean参数\r\n"}, {"name": "getParameterToBool", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 获取Boolean参数\r\n"}, {"name": "getParams", "paramTypes": ["jakarta.servlet.ServletRequest"], "doc": "\n 获得所有请求参数\r\n\r\n @param request 请求对象{@link ServletRequest}\r\n @return Map\r\n"}, {"name": "getParamMap", "paramTypes": ["jakarta.servlet.ServletRequest"], "doc": "\n 获得所有请求参数\r\n\r\n @param request 请求对象{@link ServletRequest}\r\n @return Map\r\n"}, {"name": "getRequest", "paramTypes": [], "doc": "\n 获取request\r\n"}, {"name": "getResponse", "paramTypes": [], "doc": "\n 获取response\r\n"}, {"name": "getSession", "paramTypes": [], "doc": "\n 获取session\r\n"}, {"name": "renderString", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.String"], "doc": "\n 将字符串渲染到客户端\r\n\r\n @param response 渲染对象\r\n @param string   待渲染的字符串\r\n"}, {"name": "isAjaxRequest", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": "\n 是否是Ajax异步请求\r\n\r\n @param request\r\n"}, {"name": "urlEncode", "paramTypes": ["java.lang.String"], "doc": "\n 内容编码\r\n\r\n @param str 内容\r\n @return 编码后的内容\r\n"}, {"name": "urlDecode", "paramTypes": ["java.lang.String"], "doc": "\n 内容解码\r\n\r\n @param str 内容\r\n @return 解码后的内容\r\n"}], "constructors": []}