package org.dromara.business.domain;

import io.github.linpeilie.AutoMapperConfig__962;
import io.github.linpeilie.BaseMapper;
import org.dromara.business.domain.bo.PolicyNewsBoToPolicyNewsMapper__30;
import org.dromara.business.domain.vo.PolicyNewsVo;
import org.dromara.business.domain.vo.PolicyNewsVoToPolicyNewsMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__962.class,
    uses = {PolicyNewsVoToPolicyNewsMapper__30.class,PolicyNewsBoToPolicyNewsMapper__30.class},
    imports = {}
)
public interface PolicyNewsToPolicyNewsVoMapper__30 extends BaseMapper<PolicyNews, PolicyNewsVo> {
}
