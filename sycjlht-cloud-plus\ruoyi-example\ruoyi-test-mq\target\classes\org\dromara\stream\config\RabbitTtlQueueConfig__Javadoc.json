{"doc": "\n RabbitTTL队列\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "delayQueue", "paramTypes": [], "doc": "\n 声明延迟队列\r\n"}, {"name": "delayExchange", "paramTypes": [], "doc": "\n 声明延迟交换机\r\n"}, {"name": "delayBinding", "paramTypes": ["org.springframework.amqp.core.Queue", "org.springframework.amqp.core.CustomExchange"], "doc": "\n 将延迟队列绑定到延迟交换机\r\n"}, {"name": "deadLetter<PERSON><PERSON><PERSON>", "paramTypes": [], "doc": "\n 声明死信队列\r\n"}, {"name": "deadLetterExchange", "paramTypes": [], "doc": "\n 声明死信交换机\r\n"}, {"name": "deadLetter<PERSON><PERSON>ing", "paramTypes": ["org.springframework.amqp.core.Queue", "org.springframework.amqp.core.DirectExchange"], "doc": "\n 将死信队列绑定到死信交换机\r\n"}], "constructors": []}