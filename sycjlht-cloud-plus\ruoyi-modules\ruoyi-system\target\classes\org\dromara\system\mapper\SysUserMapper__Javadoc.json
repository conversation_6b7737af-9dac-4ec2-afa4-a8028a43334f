{"doc": "\n 用户表 数据层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectUserExportList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 根据条件分页查询用户列表\r\n\r\n @param queryWrapper 查询条件\r\n @return 用户信息集合信息\r\n"}, {"name": "selectAllocatedList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 根据条件分页查询已配用户角色列表\r\n\r\n @param page         分页信息\r\n @param queryWrapper 查询条件\r\n @return 用户信息集合信息\r\n"}, {"name": "selectUnallocatedList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 根据条件分页查询未分配用户角色列表\r\n\r\n @param queryWrapper 查询条件\r\n @return 用户信息集合信息\r\n"}], "constructors": []}