{"doc": "\n 公司和学校的员工申请记录Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-09-05\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "initProvincialNameToIdMap", "paramTypes": [], "doc": "\n 初始化省份名称到 ID 的映射\r\n"}, {"name": "selectNekndEmployeeApplicationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询公司和学校的员工申请记录\r\n\r\n @param id 公司和学校的员工申请记录主键\r\n @return 公司和学校的员工申请记录\r\n"}, {"name": "selectNekndEmployeeApplicationList", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": "\n 查询公司和学校的员工申请记录列表\r\n\r\n @param nekndEmployeeApplication 公司和学校的员工申请记录\r\n @return 公司和学校的员工申请记录\r\n"}, {"name": "insertNekndEmployeeApplication", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": "\n 新增公司和学校的员工申请记录\r\n\r\n @param nekndEmployeeApplication 公司和学校的员工申请记录\r\n @return 结果\r\n"}, {"name": "updateNekndEmployeeApplication", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": "\n 修改公司和学校的员工申请记录\r\n\r\n @param nekndEmployeeApplication 公司和学校的员工申请记录\r\n @return 结果\r\n"}, {"name": "deleteNekndEmployeeApplicationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除公司和学校的员工申请记录\r\n\r\n @param ids 需要删除的公司和学校的员工申请记录主键\r\n @return 结果\r\n"}, {"name": "deleteNekndEmployeeApplicationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除公司和学校的员工申请记录信息\r\n\r\n @param id 公司和学校的员工申请记录主键\r\n @return 结果\r\n"}, {"name": "validateUserApplication", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": "\n 校验用户申请\r\n\r\n @param userId 用户ID\r\n @param companyId 公司ID\r\n @param reviewStatus 审核状态\r\n @return 结果\r\n"}], "constructors": []}