package org.dromara.business.domain;

import io.github.linpeilie.AutoMapperConfig__1006;
import io.github.linpeilie.BaseMapper;
import org.dromara.business.domain.bo.PolicyNewsBoToPolicyNewsMapper__1;
import org.dromara.business.domain.vo.PolicyNewsVo;
import org.dromara.business.domain.vo.PolicyNewsVoToPolicyNewsMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1006.class,
    uses = {PolicyNewsVoToPolicyNewsMapper__1.class,PolicyNewsBoToPolicyNewsMapper__1.class},
    imports = {}
)
public interface PolicyNewsToPolicyNewsVoMapper__1 extends BaseMapper<PolicyNews, PolicyNewsVo> {
}
