{"doc": "\n 任务信息Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-03-02\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getListRunTask", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 获取待办信息\r\n\r\n @param page         分页\r\n @param queryWrapper 条件\r\n @return 结果\r\n"}, {"name": "getListRunTask", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 获取待办信息\r\n\r\n @param queryWrapper 条件\r\n @return 结果\r\n"}, {"name": "getListFinishTask", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 获取已办\r\n\r\n @param page         分页\r\n @param queryWrapper 条件\r\n @return 结果\r\n"}, {"name": "getTaskCopyByPage", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.query.QueryWrapper"], "doc": "\n 查询当前用户的抄送\r\n\r\n @param page         分页\r\n @param queryWrapper 条件\r\n @return 结果\r\n"}], "constructors": []}