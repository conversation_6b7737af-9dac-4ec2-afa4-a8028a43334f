{"doc": " 任务信息Mapper接口\n\n <AUTHOR>\n @date 2024-03-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "getListRunTask", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 获取待办信息\n\n @param page         分页\n @param queryWrapper 条件\n @return 结果\n"}, {"name": "getListRunTask", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 获取待办信息\n\n @param queryWrapper 条件\n @return 结果\n"}, {"name": "getListFinishTask", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 获取已办\n\n @param page         分页\n @param queryWrapper 条件\n @return 结果\n"}, {"name": "getTaskCopyByPage", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.query.QueryWrapper"], "doc": " 查询当前用户的抄送\n\n @param page         分页\n @param queryWrapper 条件\n @return 结果\n"}], "constructors": []}