package com.neknd.system.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import com.neknd.common.annotation.Anonymous;
import com.neknd.system.domain.NekndProvincialCity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.comparator.Comparators;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.neknd.common.core.controller.BaseController;
import com.neknd.common.core.domain.AjaxResult;
import com.neknd.system.domain.NekndProvincial;
import com.neknd.system.service.INekndProvincialService;

/**
 * 省级Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/system/provincial")
public class NekndProvincialController extends BaseController
{
    @Autowired
    private INekndProvincialService nekndProvincialService;


    /**
     * 获取所有省份
     */
//    @Anonymous
//    @GetMapping(value = "/getList")
//    public AjaxResult getList()
//    {
//        List<NekndProvincial> list = nekndProvincialService.selectNekndProvincialList(new NekndProvincial());
//        return success(list);
//    }

    /**
     * 获取省级详细信息
     */
    @Anonymous
    @GetMapping(value = "/{pid}")
    public AjaxResult getInfo(@PathVariable("pid") Long pid)
    {
        return success(nekndProvincialService.selectNekndProvincialByPid(pid));
    }

    /**
     * 湖北省咸宁市放地区筛选的第一位
     */
    @Anonymous
    @GetMapping(value = "/getList")
    public AjaxResult getList()
    {
        List<NekndProvincial> list = nekndProvincialService.selectNekndProvincialList(new NekndProvincial());
        list.add(0, list.remove(17));
        return success(list);
    }

}
