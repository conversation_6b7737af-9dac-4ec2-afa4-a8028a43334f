{"doc": "\n 异步任务管理器\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "OPERATE_DELAY_TIME", "doc": "\n 操作延迟10毫秒\r\n"}, {"name": "executor", "doc": "\n 异步操作任务调度线程池\r\n"}], "enumConstants": [], "methods": [{"name": "execute", "paramTypes": ["java.util.TimerTask"], "doc": "\n 执行任务\r\n\r\n @param task 任务\r\n"}, {"name": "shutdown", "paramTypes": [], "doc": "\n 停止任务线程池\r\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "\n 单例模式\r\n"}]}