{"doc": "\n 关于平台信息编辑Controller\r\n \r\n <AUTHOR>\r\n @date 2025-02-05\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询关于平台信息编辑列表\r\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": "\n 导出关于平台信息编辑列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取关于平台信息编辑详细信息\r\n"}, {"name": "getAdminInfo", "paramTypes": ["java.lang.Integer"], "doc": "\n 管理端获取关于平台信息详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": "\n 新增关于平台信息编辑\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": "\n 修改关于平台信息编辑\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 删除关于平台信息编辑\r\n"}, {"name": "getLatestAbout", "paramTypes": [], "doc": "\n 获取最新的关于平台信息\r\n"}, {"name": "getAboutByPlatform", "paramTypes": ["java.lang.String"], "doc": "\n 按平台类型获取关于信息\r\n"}, {"name": "publish", "paramTypes": ["java.lang.Integer"], "doc": "\n 发布关于平台信息\r\n"}, {"name": "unpublish", "paramTypes": ["java.lang.Integer"], "doc": "\n 下线关于平台信息\r\n"}], "constructors": []}