package org.dromara.workflow.domain.vo;

import io.github.linpeilie.AutoMapperConfig__961;
import io.github.linpeilie.BaseMapper;
import org.dromara.workflow.domain.FlowCategory;
import org.dromara.workflow.domain.FlowCategoryToFlowCategoryVoMapper__14;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__961.class,
    uses = {FlowCategoryToFlowCategoryVoMapper__14.class,FlowCategoryToFlowCategoryVoMapper__14.class},
    imports = {}
)
public interface FlowCategoryVoToFlowCategoryMapper__14 extends BaseMapper<FlowCategoryVo, FlowCategory> {
}
