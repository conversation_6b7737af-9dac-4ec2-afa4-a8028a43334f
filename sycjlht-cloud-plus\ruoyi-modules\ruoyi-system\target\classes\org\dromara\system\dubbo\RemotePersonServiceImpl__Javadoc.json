{"doc": "\n 人员信息远程服务实现\r\n\r\n <AUTHOR>\r\n @date 2025-08-02\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPersonByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询人员信息\r\n\r\n @param userId 用户ID\r\n @return 人员信息\r\n"}, {"name": "selectPersonVoList", "paramTypes": ["org.dromara.system.api.domain.vo.RemotePersonVo"], "doc": "\n 查询人员信息列表\r\n\r\n @param person 查询条件\r\n @return 人员信息列表\r\n"}, {"name": "queryByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询人员信息（简化版）\r\n\r\n @param userId 用户ID\r\n @return 人员信息\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据用户ID查询人员信息\r\n\r\n @param userId 用户ID\r\n @return 人员信息\r\n"}, {"name": "updateCompanyInfo", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String"], "doc": "\n 更新人员公司信息\r\n\r\n @param userId 用户ID\r\n @param deptId 部门ID\r\n @param companyName 公司名称\r\n @return 更新结果\r\n"}], "constructors": []}